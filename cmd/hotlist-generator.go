package main

import (
	"bufio"
	"flag"
	"fmt"
	"log"
	"newsBot/internal/models"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"newsBot/internal/core"
	"newsBot/internal/hotlist"
	"newsBot/internal/llm"
	"newsBot/internal/model"
	"newsBot/internal/notification"
	"newsBot/internal/svc"
	"newsBot/internal/vector"
)

// loadEnvFile 加载.env文件
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	return scanner.Err()
}

func main() {
	// 加载环境变量
	if err := loadEnvFile(".env"); err != nil {
		log.Printf("⚠️ 加载.env文件失败: %v", err)
		log.Println("💡 将尝试使用系统环境变量")
	} else {
		log.Println("✅ 成功加载.env配置文件")
	}

	// 命令行参数
	var (
		runOnce     = flag.Bool("once", false, "只运行一次，不启动定时任务")
		interval    = flag.Int("interval", 24, "定时任务间隔（小时）")
		healthCheck = flag.Bool("health", false, "执行健康检查")
		showStatus  = flag.Bool("status", false, "显示系统状态")
		demoMode    = flag.Bool("demo", false, "演示模式（无需API密钥）")
	)
	flag.Parse()

	log.Println("🔥 热榜内容生成程序启动...")
	log.Println("🎯 专注于热榜抓取、话题聚合、新闻查询和文章生成")

	// 检查环境变量（演示模式跳过）
	if !*demoMode {
		if err := checkEnvironment(); err != nil {
			log.Printf("❌ 环境检查失败: %v", err)
			log.Println("💡 提示：使用 -demo 参数可以在演示模式下运行")
			return
		}
	} else {
		log.Println("🎭 演示模式：跳过环境变量检查")
	}

	// 初始化组件
	orchestrator, err := initializeSystem(*demoMode)
	if err != nil {
		log.Fatalf("❌ 系统初始化失败: %v", err)
	}

	// 健康检查
	if *healthCheck {
		performHealthCheck(orchestrator)
		return
	}

	// 显示状态
	if *showStatus {
		showSystemStatus(orchestrator)
		return
	}

	// 执行工作流
	if *runOnce {
		log.Println("🚀 执行单次热榜内容生成工作流...")
		result := orchestrator.RunDailyWorkflow()
		printWorkflowResult(&result)
	} else {
		log.Printf("⏰ 启动定时任务，每 %d 小时执行一次", *interval)
		runScheduledWorkflow(orchestrator, *interval)
	}
}

// checkEnvironment 检查环境变量
func checkEnvironment() error {
	requiredEnvs := []string{
		"DASHSCOPE_API_KEY",
		"SMTP_HOST",
		"SMTP_PORT",
		"SMTP_USERNAME",
		"SMTP_PASSWORD",
		"EMAIL_TO",
	}

	for _, env := range requiredEnvs {
		if os.Getenv(env) == "" {
			return fmt.Errorf("缺少必需的环境变量: %s", env)
		}
	}

	return nil
}

// initializeSystem 初始化系统组件
func initializeSystem(demoMode bool) (*core.Orchestrator, error) {
	log.Println("🔧 初始化系统组件...")

	// 服务上下文已通过init()函数自动初始化
	if svc.SvcCtx == nil {
		return nil, fmt.Errorf("服务上下文初始化失败")
	}
	log.Println("✅ 服务上下文初始化成功")

	// 初始化热榜爬虫
	crawlers := []core.HotlistCrawler{
		hotlist.NewWeiboCrawler(),
		hotlist.NewZhihuCrawler(),
		hotlist.NewBaiduCrawler(),
	}
	log.Println("✅ 热榜爬虫初始化完成")

	// 初始化LLM客户端
	var llmClient core.LLMClient
	if demoMode {
		llmClient = &DemoLLMClient{}
		log.Println("🎭 使用演示模式LLM客户端")
	} else {
		llmClient = llm.NewDashScopeLLMClient()
		log.Println("✅ LLM客户端初始化成功")
	}

	// 初始化向量服务
	var vectorService *vector.HTTPService
	var err error
	if !demoMode {
		vectorService, err = vector.NewHTTPService()
		if err != nil {
			log.Printf("⚠️ 向量服务初始化失败: %v", err)
			log.Println("将在没有向量服务的情况下运行")
		} else {
			log.Println("✅ 向量服务初始化成功")
		}
	}

	// 初始化通知服务
	var notifier core.Notifier
	if demoMode {
		notifier = &DemoNotifier{}
		log.Println("🎭 使用演示模式通知服务")
	} else {
		notifier = notification.NewEmailNotifier()
		log.Println("✅ 邮件通知服务初始化成功")
	}

	// 创建组件
	topicStorage := &TopicStorageAdapter{model: model.NewTopicAggregationModel(svc.SvcCtx.DB)}
	topicAggregator := hotlist.NewAggregatorService(crawlers, llmClient, topicStorage)
	contentGenerator := &ContentGenerator{llmClient: llmClient}
	newsRetriever := &NewsRetriever{vectorService: vectorService}
	articleStorage := &ArticleStorageAdapter{model: model.NewGeneratedArticleModel(svc.SvcCtx.DB)}

	log.Println("✅ 数据库初始化完成")

	// 创建编排器
	orchestrator := core.NewOrchestrator(
		topicAggregator,
		contentGenerator,
		notifier,
		newsRetriever,
		topicStorage,
		articleStorage,
	)

	log.Println("🎉 系统初始化完成")
	return orchestrator, nil
}

// runScheduledWorkflow 运行定时工作流
func runScheduledWorkflow(orchestrator *core.Orchestrator, intervalHours int) {
	// 立即执行一次
	go func() {
		log.Println("🚀 执行初始热榜内容生成工作流...")
		result := orchestrator.RunDailyWorkflow()
		printWorkflowResult(&result)
	}()

	// 定时任务
	ticker := time.NewTicker(time.Duration(intervalHours) * time.Hour)
	defer ticker.Stop()

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Printf("⏰ 定时任务已启动，每 %d 小时执行一次。按 Ctrl+C 停止。", intervalHours)

	for {
		select {
		case <-ticker.C:
			log.Println("🚀 执行定时热榜内容生成工作流...")
			result := orchestrator.RunDailyWorkflow()
			printWorkflowResult(&result)
		case <-sigChan:
			log.Println("收到停止信号，正在关闭...")
			return
		}
	}
}

// printWorkflowResult 打印工作流结果
func printWorkflowResult(result *core.WorkflowResult) {
	log.Println("\n=== 工作流执行结果 ===")
	log.Printf("🎯 聚合话题: %d 个", result.TopicsCount)
	log.Printf("📝 生成文章: %d 篇", result.ArticlesCount)
	log.Printf("⏱️ 执行时间: %s", result.Duration)
	log.Printf("✅ 执行状态: %v", result.Success)

	if result.Error != "" {
		log.Printf("❌ 错误信息: %s", result.Error)
	}

	log.Println("========================")
}

// performHealthCheck 执行健康检查
func performHealthCheck(orchestrator *core.Orchestrator) {
	log.Println("🔍 执行健康检查...")
	// 这里可以添加具体的健康检查逻辑
	log.Println("✅ 健康检查通过")
}

// showSystemStatus 显示系统状态
func showSystemStatus(orchestrator *core.Orchestrator) {
	log.Println("📊 系统状态信息...")
	// 这里可以添加具体的状态显示逻辑
	log.Println("✅ 系统运行正常")
}

// DemoLLMClient 演示模式LLM客户端
type DemoLLMClient struct{}

func (d *DemoLLMClient) Aggregate(items []core.HotItem) ([]core.Topic, error) {
	// 演示模式：生成模拟话题
	topics := []core.Topic{
		{
			ID:          fmt.Sprintf("demo_topic_%d", time.Now().Unix()),
			Title:       "科技发展趋势",
			Description: "人工智能、区块链等新兴技术的发展动态",
			HotValue:    85.5,
			Categories:  []string{"科技", "创新"},
			CreatedAt:   time.Now(),
		},
		{
			ID:          fmt.Sprintf("demo_topic_%d", time.Now().Unix()+1),
			Title:       "社会热点话题",
			Description: "当前社会关注的重要议题和事件",
			HotValue:    78.2,
			Categories:  []string{"社会", "民生"},
			CreatedAt:   time.Now(),
		},
	}

	log.Printf("🎭 演示模式：生成了 %d 个话题", len(topics))
	return topics, nil
}

func (d *DemoLLMClient) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	// 演示模式：生成模拟文章
	article := &core.Article{
		Id:        fmt.Sprintf("demo_article_%d", time.Now().Unix()),
		Title:     fmt.Sprintf("深度解读：%s", topic.Title),
		Content:   fmt.Sprintf("这是关于'%s'的深度分析文章。\n\n%s\n\n本文从多个角度分析了相关问题，提供了专业的见解和建议。文章内容丰富，逻辑清晰，适合各类读者阅读。", topic.Title, topic.Description),
		Summary:   fmt.Sprintf("本文深度分析了%s，提供了专业见解。", topic.Title),
		TopicId:   topic.ID,
		WordCount: 150,
		CreatedAt: time.Now(),
		Status:    "draft",
	}

	log.Printf("🎭 演示模式：生成了文章 '%s'", article.Title)
	return article, nil
}

func (d *DemoLLMClient) GenerateSummary(content string) (string, error) {
	return "这是演示模式生成的摘要内容。", nil
}

// DemoNotifier 演示模式通知服务
type DemoNotifier struct{}

func (d *DemoNotifier) SendToEvernote(articles []core.Article) error {
	log.Printf("🎭 演示模式：模拟发送 %d 篇文章到印象笔记", len(articles))
	for _, article := range articles {
		log.Printf("  - %s (%d字)", article.Title, article.WordCount)
	}
	return nil
}

func (d *DemoNotifier) SendEmail(subject, content string, recipients []string) error {
	log.Printf("🎭 演示模式：发送邮件")
	log.Printf("  - 主题: %s", subject)
	log.Printf("  - 收件人: %v", recipients)
	log.Printf("  - 内容长度: %d字符", len(content))
	return nil
}

func (d *DemoNotifier) SendSummaryReport(result core.WorkflowResult) error {
	log.Printf("🎭 演示模式：发送汇总报告")
	log.Printf("  - 话题数量: %d", result.TopicsCount)
	log.Printf("  - 文章数量: %d", result.ArticlesCount)
	log.Printf("  - 执行状态: %v", result.Success)
	log.Printf("  - 执行时间: %s", result.Duration)
	return nil
}

// ContentGenerator 内容生成器实现
type ContentGenerator struct {
	llmClient core.LLMClient
}

func (c *ContentGenerator) GenerateArticles(topics []core.Topic) ([]core.Article, error) {
	var articles []core.Article

	for _, topic := range topics {
		article, err := c.llmClient.GenerateArticle(topic, []string{})
		if err != nil {
			log.Printf("⚠️ 话题 '%s' 文章生成失败: %v", topic.Title, err)
			continue
		}
		articles = append(articles, *article)
	}

	return articles, nil
}

func (c *ContentGenerator) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	return c.llmClient.GenerateArticle(topic, relatedNews)
}

// NewsRetriever 新闻检索器实现
type NewsRetriever struct {
	vectorService *vector.HTTPService
}

func (n *NewsRetriever) SearchRelatedNews(topic core.Topic, limit int) ([]models.NewsItem, error) {
	if n.vectorService == nil {
		log.Println("🎭 演示模式：跳过向量搜索")
		return []models.NewsItem{}, nil
	}

	// 使用话题标题和描述进行搜索
	searchText := topic.Title + " " + topic.Description
	results, err := n.vectorService.SearchByText(searchText, limit)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// TopicStorageAdapter 话题存储适配器
type TopicStorageAdapter struct {
	model *model.TopicAggregationModel
}

func (t *TopicStorageAdapter) SaveTopics(topics []core.Topic) error {
	// 这里只是记录话题，实际存储由聚合器处理
	log.Printf("💾 保存 %d 个话题到数据库", len(topics))
	return nil
}

func (t *TopicStorageAdapter) GetTopics(limit int) ([]core.Topic, error) {
	// 返回空列表，因为我们主要用于记录
	return []core.Topic{}, nil
}

func (t *TopicStorageAdapter) GetTopicByID(id string) (*core.Topic, error) {
	return nil, fmt.Errorf("topic not found")
}

func (t *TopicStorageAdapter) UpdateTopic(topic core.Topic) error {
	return nil
}

func (t *TopicStorageAdapter) DeleteTopic(id string) error {
	return nil
}

// ArticleStorageAdapter 文章存储适配器
type ArticleStorageAdapter struct {
	model *model.GeneratedArticleModel
}

func (a *ArticleStorageAdapter) SaveArticle(article core.Article) error {
	// 保存文章记录到数据库
	record := model.GeneratedArticleRecord{
		TopicId:      article.TopicId,
		TopicTitle:   "Generated Topic",
		ArticleTitle: article.Title,
		ArticleId:    article.Id,
		WordCount:    article.WordCount,
		LLMModel:     "Demo",
		Status:       "success",
	}
	return a.model.SaveGeneratedArticleRecord(record)
}

func (a *ArticleStorageAdapter) GetArticles(limit int) ([]core.Article, error) {
	return []core.Article{}, nil
}

func (a *ArticleStorageAdapter) GetArticleByID(id string) (*core.Article, error) {
	return nil, fmt.Errorf("article not found")
}

func (a *ArticleStorageAdapter) UpdateArticle(article core.Article) error {
	return nil
}

func (a *ArticleStorageAdapter) DeleteArticle(id string) error {
	return nil
}

func (a *ArticleStorageAdapter) GetArticlesByStatus(status string) ([]core.Article, error) {
	return []core.Article{}, nil
}
