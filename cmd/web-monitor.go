package main

import (
	"bufio"
	"flag"
	"log"
	"newsBot/internal/api"
	"os"
	"os/signal"
	"strings"
	"syscall"
)

// loadEnvFile 加载.env文件
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	return scanner.Err()
}

func main() {
	// 加载环境变量
	if err := loadEnvFile(".env"); err != nil {
		log.Printf("加载.env文件失败: %v", err)
	}

	// 命令行参数
	var (
		port = flag.Int("port", 8080, "Web服务器端口")
	)
	flag.Parse()

	log.Println("🌐 Web监控服务器启动...")
	log.Println("🎯 专注于工作流监控和Web界面")

	// 启动API服务器
	server, err := api.NewServer(*port)
	if err != nil {
		log.Fatalf("创建API服务器失败: %v", err)
	}
	defer server.Close()

	// 在后台启动API服务器
	go func() {
		if err := server.Start(); err != nil {
			log.Fatalf("API服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Printf("🌐 Web监控服务已启动:")
	log.Printf("- 监控界面: http://localhost:%d", *port)
	log.Printf("- 新闻爬取监控: http://localhost:%d/quality", *port)
	log.Printf("- 热榜工作流监控: http://localhost:%d/hotlist", *port)
	log.Println("按 Ctrl+C 停止服务")

	// 阻塞等待信号
	<-sigChan
	log.Println("收到停止信号，正在关闭服务...")
}
