package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"newsBot/internal/core"
	"newsBot/internal/hotlist"
	"newsBot/internal/llm"
	"newsBot/internal/notification"
	"newsBot/internal/storage"
	"newsBot/internal/svc"
	"newsBot/internal/vector"
	"newsBot/models"
)

// loadEnvFile 加载.env文件
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	return scanner.Err()
}

func main() {
	// 加载环境变量
	if err := loadEnvFile(".env"); err != nil {
		log.Printf("⚠️ 加载.env文件失败: %v", err)
		log.Println("💡 将尝试使用系统环境变量")
	} else {
		log.Println("✅ 成功加载.env配置文件")
	}

	// 命令行参数
	var (
		runOnce     = flag.Bool("once", false, "只运行一次，不启动定时任务")
		interval    = flag.Int("interval", 24, "定时任务间隔（小时）")
		healthCheck = flag.Bool("health", false, "执行健康检查")
		showStatus  = flag.Bool("status", false, "显示系统状态")
		demoMode    = flag.Bool("demo", false, "演示模式（无需API密钥）")
	)
	flag.Parse()

	log.Println("🤖 全流程自动化内容生成系统启动...")
	log.Println("📋 基于add.md设计的完整工作流程")

	// 检查环境变量（演示模式跳过）
	if !*demoMode {
		if err := checkEnvironment(); err != nil {
			log.Printf("❌ 环境检查失败: %v", err)
			log.Println("💡 提示：使用 -demo 参数可以在演示模式下运行")
			return
		}
	} else {
		log.Println("🎭 演示模式：跳过环境变量检查")
	}

	// 初始化组件
	orchestrator, err := initializeSystem(*demoMode)
	if err != nil {
		log.Fatalf("❌ 系统初始化失败: %v", err)
	}

	// 健康检查
	if *healthCheck {
		performHealthCheck(orchestrator)
		return
	}

	// 显示状态
	if *showStatus {
		showSystemStatus(orchestrator)
		return
	}

	// 执行工作流
	if *runOnce {
		log.Println("🚀 执行单次内容生成工作流...")
		result := orchestrator.RunDailyWorkflow()
		printWorkflowResult(result)

		// 导出生成的文章到文件
		if result.ArticlesCount > 0 {
			exportGeneratedContent(orchestrator)
		}
	} else {
		log.Printf("⏰ 启动定时任务，每 %d 小时执行一次", *interval)
		runScheduledWorkflow(orchestrator, *interval)
	}
}

// checkEnvironment 检查环境变量
func checkEnvironment() error {
	// 只检查必需的LLM相关环境变量
	required := []string{
		"DASHSCOPE_API_KEY",
	}

	for _, env := range required {
		if os.Getenv(env) == "" {
			return fmt.Errorf("缺少必需的环境变量: %s", env)
		}
	}

	// 检查邮件配置（可选）
	emailVars := []string{"SMTP_USER", "SMTP_PASSWORD", "EVERNOTE_EMAIL"}
	emailConfigured := true
	for _, env := range emailVars {
		if os.Getenv(env) == "" || strings.Contains(os.Getenv(env), "your-") {
			emailConfigured = false
			break
		}
	}

	if !emailConfigured {
		log.Println("⚠️ 邮件配置未设置，将跳过邮件发送功能")
		log.Println("💡 如需邮件功能，请在 .env 文件中配置真实的邮件信息")
	}

	log.Println("✅ 环境变量检查通过")
	return nil
}

// isEmailConfigured 检查邮件配置是否可用
func isEmailConfigured() bool {
	emailVars := map[string]string{
		"SMTP_USER":      os.Getenv("SMTP_USER"),
		"SMTP_PASSWORD":  os.Getenv("SMTP_PASSWORD"),
		"EVERNOTE_EMAIL": os.Getenv("EVERNOTE_EMAIL"),
	}

	for key, value := range emailVars {
		if value == "" || strings.Contains(value, "your-") || strings.Contains(value, "example.com") {
			log.Printf("⚠️ 邮件配置项 %s 未正确设置", key)
			return false
		}
	}

	return true
}

// initializeSystem 初始化系统组件
func initializeSystem(demoMode bool) (*core.Orchestrator, error) {
	log.Println("🔧 初始化系统组件...")

	// 初始化LLM客户端
	var llmClient core.LLMClient
	if demoMode {
		llmClient = NewDemoLLMClient()
		log.Println("✅ LLM客户端初始化完成（演示模式）")
	} else {
		// 优先使用 Fireworks AI，如果失败则回退到通义千问
		llmClient = llm.NewFireworksLLMClient()
		log.Println("✅ LLM客户端初始化完成（Fireworks AI）")
	}

	// 向量化功能已内置，无需单独初始化
	log.Println("✅ 向量化功能已就绪")

	// 初始化存储
	topicStorage := storage.NewMemoryTopicStorage()
	articleStorage := storage.NewMemoryArticleStorage()
	log.Println("✅ 存储组件初始化完成")

	// 初始化热榜爬虫
	crawlers := []core.HotlistCrawler{
		hotlist.NewWeiboCrawler(),
		hotlist.NewZhihuCrawler(),
		hotlist.NewBaiduCrawler(),
	}
	log.Println("✅ 热榜爬虫初始化完成")

	// 初始化话题聚合器
	topicAggregator := hotlist.NewAggregatorService(crawlers, llmClient, topicStorage)
	log.Println("✅ 话题聚合器初始化完成")

	// 初始化内容生成器
	contentGenerator := NewContentGenerator(llmClient)
	log.Println("✅ 内容生成器初始化完成")

	// 初始化通知服务
	var notifier core.Notifier
	if demoMode {
		notifier = NewDemoNotifier()
		log.Println("✅ 通知服务初始化完成（演示模式）")
	} else {
		// 检查邮件配置是否可用
		if isEmailConfigured() {
			notifier = notification.NewEmailNotifier()
			log.Println("✅ 通知服务初始化完成（邮件模式）")
		} else {
			notifier = NewDemoNotifier()
			log.Println("✅ 通知服务初始化完成（演示模式 - 邮件配置不可用）")
		}
	}

	// 初始化新闻检索器
	newsRetriever := NewNewsRetriever()
	log.Println("✅ 新闻检索器初始化完成")

	// 服务上下文已通过init()函数自动初始化
	if svc.SvcCtx == nil {
		log.Fatalf("服务上下文初始化失败")
	}
	log.Println("✅ 数据库初始化完成")

	// 创建编排器
	orchestrator := core.NewOrchestrator(
		topicAggregator,
		contentGenerator,
		notifier,
		newsRetriever,
		topicStorage,
		articleStorage,
	)

	log.Println("🎉 系统初始化完成")
	return orchestrator, nil
}

// performHealthCheck 执行健康检查
func performHealthCheck(orchestrator *core.Orchestrator) {
	log.Println("🏥 执行系统健康检查...")

	status := orchestrator.GetSystemStatus()

	fmt.Println("\n📊 系统状态报告:")
	fmt.Printf("• 最近话题数量: %v\n", status["recent_topics_count"])
	fmt.Printf("• 最近文章数量: %v\n", status["recent_articles_count"])
	fmt.Printf("• 待审核文章: %v\n", status["draft_articles_count"])
	fmt.Printf("• 系统健康状态: %v\n", status["system_healthy"])
	fmt.Printf("• 检查时间: %v\n", status["last_check"])

	if status["system_healthy"].(bool) {
		log.Println("✅ 系统健康检查通过")
	} else {
		log.Println("⚠️ 系统存在问题，请检查日志")
	}
}

// showSystemStatus 显示系统状态
func showSystemStatus(orchestrator *core.Orchestrator) {
	log.Println("📊 获取系统状态...")

	status := orchestrator.GetSystemStatus()

	fmt.Println("\n=== 系统状态 ===")
	for key, value := range status {
		fmt.Printf("%s: %v\n", key, value)
	}
}

// runScheduledWorkflow 运行定时工作流
func runScheduledWorkflow(orchestrator *core.Orchestrator, intervalHours int) {
	ticker := time.NewTicker(time.Duration(intervalHours) * time.Hour)
	defer ticker.Stop()

	// 立即执行一次
	log.Println("🚀 执行初始工作流...")
	result := orchestrator.RunDailyWorkflow()
	printWorkflowResult(result)

	// 定时执行
	for range ticker.C {
		log.Printf("⏰ 定时任务触发 - %s", time.Now().Format("2006-01-02 15:04:05"))
		result := orchestrator.RunDailyWorkflow()
		printWorkflowResult(result)
	}
}

// printWorkflowResult 打印工作流结果
func printWorkflowResult(result core.WorkflowResult) {
	fmt.Println("\n=== 工作流执行结果 ===")
	fmt.Printf("执行时间: %s\n", result.ExecutedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("执行状态: %s\n", map[bool]string{true: "✅ 成功", false: "❌ 失败"}[result.Success])
	fmt.Printf("话题数量: %d\n", result.TopicsCount)
	fmt.Printf("文章数量: %d\n", result.ArticlesCount)
	fmt.Printf("执行耗时: %s\n", result.Duration)

	if result.Error != "" {
		fmt.Printf("错误信息: %s\n", result.Error)
	}

	fmt.Println("========================")
}

// ContentGenerator 内容生成器实现
type ContentGenerator struct {
	llmClient core.LLMClient
}

func NewContentGenerator(llmClient core.LLMClient) *ContentGenerator {
	return &ContentGenerator{llmClient: llmClient}
}

func (c *ContentGenerator) GenerateArticles(topics []core.Topic) ([]core.Article, error) {
	var articles []core.Article

	for _, topic := range topics {
		article, err := c.llmClient.GenerateArticle(topic, []string{})
		if err != nil {
			log.Printf("⚠️ 话题 '%s' 文章生成失败: %v", topic.Title, err)
			continue
		}
		articles = append(articles, *article)
	}

	return articles, nil
}

func (c *ContentGenerator) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	return c.llmClient.GenerateArticle(topic, relatedNews)
}

// NewsRetriever 新闻检索器实现
type NewsRetriever struct {
	vectorService *vector.HTTPService
}

func NewNewsRetriever() *NewsRetriever {
	// 尝试初始化向量服务
	vectorService, err := vector.NewHTTPService()
	if err != nil {
		log.Printf("向量服务初始化失败: %v", err)
		vectorService = nil
	}

	return &NewsRetriever{
		vectorService: vectorService,
	}
}

func (n *NewsRetriever) SearchRelatedNews(topic core.Topic, limit int) ([]models.NewsItem, error) {
	if n.vectorService == nil {
		// 如果向量服务不可用，返回空列表
		return []models.NewsItem{}, nil
	}

	// 构建搜索查询
	searchQuery := topic.Title + " " + topic.Description

	// 使用向量搜索
	results, err := n.vectorService.SearchByText(searchQuery, limit)
	if err != nil {
		log.Printf("向量搜索失败: %v", err)
		return []models.NewsItem{}, err
	}

	return results, nil
}

// DemoLLMClient 演示模式LLM客户端
type DemoLLMClient struct{}

func NewDemoLLMClient() *DemoLLMClient {
	return &DemoLLMClient{}
}

func (d *DemoLLMClient) Aggregate(items []core.HotItem) ([]core.Topic, error) {
	// 演示模式：生成模拟话题
	topics := []core.Topic{
		{
			ID:          "demo_topic_1",
			Title:       "人工智能技术发展趋势",
			Description: "探讨当前人工智能技术的最新发展动态和未来趋势",
			HotValue:    95.5,
			Categories:  []string{"科技", "AI"},
			Platform:    "聚合",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          "demo_topic_2",
			Title:       "经济政策解读分析",
			Description: "深度分析最新经济政策对市场和民生的影响",
			HotValue:    88.2,
			Categories:  []string{"经济", "政策"},
			Platform:    "聚合",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	log.Printf("🎭 演示模式：生成了 %d 个模拟话题", len(topics))
	return topics, nil
}

func (d *DemoLLMClient) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	// 演示模式：生成模拟文章
	article := &core.Article{
		Id:        fmt.Sprintf("demo_article_%d", time.Now().Unix()),
		Title:     fmt.Sprintf("深度解读：%s", topic.Title),
		Content:   fmt.Sprintf("这是关于'%s'的深度分析文章。\n\n%s\n\n本文从多个角度分析了相关问题，提供了专业的见解和建议。文章内容丰富，逻辑清晰，适合各类读者阅读。", topic.Title, topic.Description),
		Summary:   fmt.Sprintf("本文深度分析了%s，提供了专业见解。", topic.Title),
		TopicId:   topic.ID,
		WordCount: 150,
		CreatedAt: time.Now(),
		Status:    "draft",
	}

	log.Printf("🎭 演示模式：生成了文章 '%s'", article.Title)
	return article, nil
}

func (d *DemoLLMClient) GenerateSummary(content string) (string, error) {
	return "这是演示模式生成的摘要内容。", nil
}

// DemoNotifier 演示模式通知器
type DemoNotifier struct{}

func NewDemoNotifier() *DemoNotifier {
	return &DemoNotifier{}
}

func (d *DemoNotifier) SendToEvernote(articles []core.Article) error {
	log.Printf("🎭 演示模式：模拟发送 %d 篇文章到印象笔记", len(articles))
	for i, article := range articles {
		log.Printf("  %d. %s (%d字)", i+1, article.Title, article.WordCount)
	}
	return nil
}

func (d *DemoNotifier) SendEmail(subject, content string, recipients []string) error {
	log.Printf("🎭 演示模式：模拟发送邮件 '%s' 给 %v", subject, recipients)
	return nil
}

func (d *DemoNotifier) SendSummaryReport(result core.WorkflowResult) error {
	log.Printf("🎭 演示模式：模拟发送汇总报告")
	log.Printf("  - 话题数: %d", result.TopicsCount)
	log.Printf("  - 文章数: %d", result.ArticlesCount)
	log.Printf("  - 状态: %s", map[bool]string{true: "成功", false: "失败"}[result.Success])
	return nil
}

// exportGeneratedContent 导出生成的内容到文件
func exportGeneratedContent(orchestrator *core.Orchestrator) {
	log.Println("📄 导出生成的内容到文件...")

	// 创建输出目录
	outputDir := "output"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Printf("❌ 创建输出目录失败: %v", err)
		return
	}

	timestamp := time.Now().Format("20060102_150405")

	// 创建一个简单的工作流结果文件
	resultFile := fmt.Sprintf("%s/workflow_result_%s.json", outputDir, timestamp)
	result := map[string]interface{}{
		"timestamp":  time.Now().Format("2006-01-02 15:04:05"),
		"message":    "工作流执行完成",
		"note":       "生成的文章已保存在内存中，程序结束后将丢失",
		"suggestion": "使用独立的导出工具: go run cmd/export_articles.go",
	}

	if data, err := json.MarshalIndent(result, "", "  "); err == nil {
		if err := os.WriteFile(resultFile, data, 0644); err == nil {
			log.Printf("✅ 工作流结果已保存到: %s", resultFile)
		}
	}

	log.Println("💡 注意：由于使用内存存储，生成的文章在程序结束后将丢失")
	log.Println("💡 建议：使用持久化存储（如数据库）来保存生成的内容")
}
