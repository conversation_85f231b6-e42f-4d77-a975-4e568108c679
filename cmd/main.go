package main

import (
	"flag"
	"fmt"
	"log"
	"newsBot/internal/crawler"
	"os"
	"os/signal"
	"syscall"
	"time"

	"newsBot/internal/svc"
	"newsBot/internal/vector"
)

func main() {
	// 命令行参数
	var (
		intervalHours = flag.Int("interval", 2, "爬取间隔时间（小时）")
		runOnce       = flag.Bool("once", false, "只运行一次，不启动定时任务")
		enableVector  = flag.Bool("vector", true, "启用向量化存储")
		healthCheck   = flag.Bool("health", false, "执行健康检查")
		cleanup       = flag.Bool("cleanup", false, "清理30天前的旧新闻")
		demoMode      = flag.Bool("demo", false, "演示模式（跳过向量化存储）")
	)
	flag.Parse()

	log.Println("📰 新闻爬取程序启动...")
	log.Println("🎯 专注于新闻爬取和向量化存储")

	if *demoMode {
		log.Println("🎭 演示模式：跳过向量化存储")
		*enableVector = false
	}

	// 服务上下文已通过init()函数自动初始化
	if svc.SvcCtx == nil {
		log.Fatalf("服务上下文初始化失败")
	}
	log.Println("✅ 服务上下文初始化成功")

	// 初始化向量化服务
	var vectorService *vector.HTTPService
	var err error

	if *enableVector {
		vectorService, err = vector.NewHTTPService()
		if err != nil {
			log.Printf("向量化服务初始化失败: %v", err)
			log.Println("将以传统模式运行（不使用向量化）")
			*enableVector = false
		} else {
			defer vectorService.Close()
			log.Println("向量化服务初始化成功")
		}
	}

	// 健康检查
	if *healthCheck {
		if err := performHealthCheck(vectorService, *enableVector); err != nil {
			log.Printf("健康检查失败: %v", err)
			os.Exit(1)
		}
		log.Println("健康检查通过")
		return
	}

	// 清理旧新闻
	if *cleanup {
		if !*enableVector {
			log.Println("清理功能需要启用向量化存储")
			os.Exit(1)
		}
		if err := vectorService.CleanOldNews(30); err != nil {
			log.Printf("清理旧新闻失败: %v", err)
			os.Exit(1)
		}
		log.Println("旧新闻清理完成")
		return
	}

	// 单次运行
	if *runOnce {
		log.Println("执行单次爬取任务...")
		if err := runCrawlTask(vectorService, *enableVector); err != nil {
			log.Printf("爬取任务失败: %v", err)
			os.Exit(1)
		}
		return
	}

	// 启动定时任务
	log.Printf("启动定时爬虫，每 %d 小时执行一次", *intervalHours)

	// 立即执行一次
	go func() {
		log.Println("执行初始爬取任务...")
		if err := runCrawlTask(vectorService, *enableVector); err != nil {
			log.Printf("初始爬取任务失败: %v", err)
		}
	}()

	// 定时任务
	ticker := time.NewTicker(time.Duration(*intervalHours) * time.Hour)
	defer ticker.Stop()

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Printf("定时爬虫已启动。按 Ctrl+C 停止。")

	for {
		select {
		case <-ticker.C:
			log.Println("执行定时爬取任务...")
			if err := runCrawlTask(vectorService, *enableVector); err != nil {
				log.Printf("定时爬取任务失败: %v", err)
			}
		case <-sigChan:
			log.Println("收到停止信号，正在关闭...")
			return
		}
	}
}

// runCrawlTask 执行爬取任务
func runCrawlTask(vectorService *vector.HTTPService, enableVector bool) error {
	// 使用GORM数据库连接
	svcCtx := svc.SvcCtx
	if svcCtx == nil {
		log.Printf("服务上下文未初始化")
		return fmt.Errorf("服务上下文未初始化")
	}

	// 开始爬取会话
	var sessionID int64
	sessionID, err := svcCtx.CrawlSessionModel.StartCrawlSession()
	if err != nil {
		log.Printf("创建爬取会话失败: %v", err)
		// 继续执行，不中断爬取流程
	}

	// 爬取新闻
	allNews := crawler.CrawlWithContentEnhancement()

	if len(allNews) == 0 {
		log.Println("未获取到新闻数据")
		// 结束会话
		if sessionID > 0 {
			svcCtx.CrawlSessionModel.EndCrawlSession(sessionID, 0, 0, "completed", "")
		}
		return nil
	}

	// 统计结果
	sourceCount := make(map[string]int)
	contentCount := 0
	successCount := 0

	for _, news := range allNews {
		sourceCount[news.Source]++
		if len(news.Content) > 20 {
			contentCount++
		}

		// 保存到数据库
		if sessionID > 0 {
			if err := svcCtx.NewsRecordModel.SaveNewsRecord(sessionID, news); err != nil {
				log.Printf("保存新闻记录失败: %v", err)
			} else {
				successCount++
			}
		}
	}

	// 输出统计信息
	log.Println("\n=== 爬取结果统计 ===")
	for source, count := range sourceCount {
		log.Printf("%s: %d 条新闻", source, count)
	}
	log.Printf("总计: %d 条新闻", len(allNews))
	log.Printf("有内容摘要: %d 条新闻", contentCount)
	log.Printf("内容覆盖率: %.1f%%", float64(contentCount)/float64(len(allNews))*100)
	log.Printf("数据库保存: %d 条新闻", successCount)

	// 向量化处理
	if enableVector && vectorService != nil {
		log.Println("开始向量化处理...")
		startTime := time.Now()

		if err := vectorService.ProcessNewsBatch(allNews); err != nil {
			log.Printf("向量化处理失败: %v", err)
			// 不返回错误，继续保存传统格式
		} else {
			duration := time.Since(startTime)
			log.Printf("向量化处理完成，耗时: %v", duration)

			// 获取集合统计信息
			if stats, err := vectorService.GetCollectionStats(); err == nil {
				log.Printf("向量数据库统计: %+v", stats)
			}
		}
	}

	// 结果已通过数据库存储，无需额外保存文件
	log.Printf("✅ 新闻爬取完成，共获取 %d 条新闻", len(allNews))

	// 结束爬取会话
	if sessionID > 0 {
		status := "completed"
		errorMsg := ""
		if successCount < len(allNews) {
			status = "partial"
			errorMsg = fmt.Sprintf("部分保存失败: %d/%d", successCount, len(allNews))
		}

		if err := svcCtx.CrawlSessionModel.EndCrawlSession(sessionID, len(allNews), successCount, status, errorMsg); err != nil {
			log.Printf("结束爬取会话失败: %v", err)
		}
	}

	return nil
}

// performHealthCheck 执行健康检查
func performHealthCheck(vectorService *vector.HTTPService, enableVector bool) error {
	log.Println("执行健康检查...")

	// 检查向量化服务
	if enableVector && vectorService != nil {
		if err := vectorService.HealthCheck(); err != nil {
			return fmt.Errorf("向量化服务健康检查失败: %v", err)
		}
		log.Println("✓ 向量化服务正常")
	}

	// 检查爬虫功能（简单测试）
	log.Println("✓ 爬虫服务正常")

	return nil
}
