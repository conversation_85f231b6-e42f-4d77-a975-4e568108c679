# 新闻向量化系统 Makefile

.PHONY: help build clean test install start-qdrant stop-qdrant docker-build docker-up docker-down

# 默认目标
help:
	@echo "新闻向量化系统构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build        - 构建所有二进制文件"
	@echo "  clean        - 清理构建文件"
	@echo "  test         - 运行测试"
	@echo "  install      - 安装依赖并构建"
	@echo "  start-qdrant - 启动Qdrant数据库"
	@echo "  stop-qdrant  - 停止Qdrant数据库"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-up    - 启动Docker Compose服务"
	@echo "  docker-down  - 停止Docker Compose服务"
	@echo "  run-crawler  - 执行单次新闻爬取"
	@echo "  run-hotlist  - 执行单次热榜生成"
	@echo "  run-monitor  - 启动Web监控服务器"

# 构建目录
BIN_DIR := bin
BUILD_FLAGS := -ldflags="-s -w"

# 创建构建目录
$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# 构建所有二进制文件
build: $(BIN_DIR)
	@echo "构建新闻爬虫系统（3个核心程序）..."
	go build $(BUILD_FLAGS) -o $(BIN_DIR)/news-crawler.exe cmd/main.go
	go build $(BUILD_FLAGS) -o $(BIN_DIR)/hotlist-generator.exe cmd/hotlist-generator.go
	go build $(BUILD_FLAGS) -o $(BIN_DIR)/web-monitor.exe cmd/web-monitor.go
	@echo "所有程序构建完成！"

# 构建向量化版本（需要Qdrant）- 已合并到主构建目标
build-vector: build
	@echo "向量化版本构建完成！（已包含在主构建中）"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -rf $(BIN_DIR)
	rm -f *.log
	@echo "清理完成！"

# 安装依赖
deps:
	@echo "安装Go依赖..."
	go mod tidy
	go mod download

# 运行测试
test:
	@echo "运行健康检查测试..."
	./$(BIN_DIR)/news-crawler.exe -health

# 完整安装
install: deps build
	@echo "创建必要目录..."
	mkdir -p data logs
	@if [ ! -f .env ]; then \
		echo "创建环境变量文件..."; \
		cp .env.example .env; \
		echo "请编辑 .env 文件设置您的API密钥"; \
	fi
	@echo "安装完成！"

# 启动Qdrant数据库
start-qdrant:
	@echo "启动Qdrant数据库..."
	docker run -d \
		--name qdrant \
		-p 6333:6333 \
		-p 6334:6334 \
		-v qdrant_data:/qdrant/storage \
		qdrant/qdrant || echo "Qdrant可能已在运行"
	@echo "等待Qdrant启动..."
	@sleep 5
	@curl -s http://localhost:6333/health > /dev/null && echo "Qdrant启动成功！" || echo "Qdrant启动失败，请检查日志"

# 停止Qdrant数据库
stop-qdrant:
	@echo "停止Qdrant数据库..."
	docker stop qdrant || true
	docker rm qdrant || true
	@echo "Qdrant已停止"

# 构建Docker镜像
docker-build:
	@echo "构建Docker镜像..."
	docker build -t news-vector:latest .
	@echo "Docker镜像构建完成！"

# 启动Docker Compose服务
docker-up:
	@echo "启动Docker Compose服务..."
	docker-compose up -d
	@echo "服务启动完成！"
	@echo "API地址: http://localhost:8080"
	@echo "Qdrant管理界面: http://localhost:6333/dashboard"

# 停止Docker Compose服务
docker-down:
	@echo "停止Docker Compose服务..."
	docker-compose down
	@echo "服务已停止"

# 执行单次新闻爬取
run-crawler: build
	@echo "执行单次新闻爬取任务..."
	./$(BIN_DIR)/news-crawler.exe -once

# 启动定时新闻爬取
run-crawler-scheduled: build
	@echo "启动定时新闻爬取服务..."
	./$(BIN_DIR)/news-crawler.exe -interval=2

# 执行单次热榜生成
run-hotlist: build
	@echo "执行单次热榜内容生成..."
	./$(BIN_DIR)/hotlist-generator.exe -once

# 启动定时热榜生成
run-hotlist-scheduled: build
	@echo "启动定时热榜内容生成..."
	./$(BIN_DIR)/hotlist-generator.exe -interval=24

# 启动Web监控服务器
run-monitor: build
	@echo "启动Web监控服务器..."
	./$(BIN_DIR)/web-monitor.exe -port=8080

# 健康检查
health: build
	@echo "执行健康检查..."
	./$(BIN_DIR)/news-crawler.exe -health

# 清理旧数据
cleanup: build
	@echo "清理30天前的旧数据..."
	./$(BIN_DIR)/news-crawler.exe -cleanup

# 开发模式（热重载）
dev:
	@echo "启动开发模式..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "请安装air: go install github.com/cosmtrek/air@latest"; \
		go run cmd/main.go -once; \
	fi

# 格式化代码
fmt:
	@echo "格式化Go代码..."
	go fmt ./...
	@echo "代码格式化完成！"

# 代码检查
lint:
	@echo "执行代码检查..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "请安装golangci-lint: https://golangci-lint.run/usage/install/"; \
		go vet ./...; \
	fi

# 生成文档
docs:
	@echo "生成API文档..."
	@if command -v swag > /dev/null; then \
		swag init -g cmd/server.go; \
	else \
		echo "请安装swag: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# 备份数据
backup:
	@echo "备份数据..."
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	mkdir -p backups; \
	tar -czf backups/newsbot_backup_$$timestamp.tar.gz data/ logs/ .env || true
	@echo "备份完成！"

# 显示版本信息
version:
	@echo "新闻向量化系统版本信息:"
	@echo "Go版本: $$(go version)"
	@echo "构建时间: $$(date)"
	@if [ -f $(BIN_DIR)/news-vector ]; then \
		echo "二进制文件: 已构建"; \
	else \
		echo "二进制文件: 未构建"; \
	fi
