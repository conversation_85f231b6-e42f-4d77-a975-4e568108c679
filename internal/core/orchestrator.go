package core

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"newsBot/internal/model"
	"newsBot/internal/svc"
	"newsBot/models"
)

// Orchestrator 系统编排器
type Orchestrator struct {
	topicAggregator TopicAggregator
	contentGen      ContentGenerator
	notifier        Notifier
	newsRetriever   NewsRetriever
	topicStorage    TopicStorage
	articleStorage  ArticleStorage
}

// NewsRetriever 新闻检索器接口
type NewsRetriever interface {
	SearchRelatedNews(topic Topic, limit int) ([]models.NewsItem, error)
}

// NewOrchestrator 创建新的编排器
func NewOrchestrator(
	agg TopicAggregator,
	gen ContentGenerator,
	notifier Notifier,
	retriever NewsRetriever,
	topicStorage TopicStorage,
	articleStorage ArticleStorage,
) *Orchestrator {
	return &Orchestrator{
		topicAggregator: agg,
		contentGen:      gen,
		notifier:        notifier,
		newsRetriever:   retriever,
		topicStorage:    topicStorage,
		articleStorage:  articleStorage,
	}
}

// RunDailyWorkflow 执行每日工作流
func (o *Orchestrator) RunDailyWorkflow() WorkflowResult {
	startTime := time.Now()
	result := WorkflowResult{
		ExecutedAt: startTime,
		Success:    false,
	}

	log.Println("🚀 开始执行每日内容生成工作流...")

	// 1. 获取聚合话题
	log.Println("📊 步骤1: 聚合热点话题...")
	topics, err := o.topicAggregator.AggregateTopics()
	if err != nil {
		result.Error = fmt.Sprintf("话题聚合失败: %v", err)
		result.Duration = time.Since(startTime).String()
		log.Printf("❌ %s", result.Error)
		return result
	}
	result.TopicsCount = len(topics)
	log.Printf("✅ 成功聚合 %d 个热点话题", len(topics))

	// 2. 为每个话题生成文章
	log.Println("📝 步骤2: 生成相关文章...")
	var allArticles []Article

	for i, topic := range topics {
		log.Printf("正在处理话题 %d/%d: %s", i+1, len(topics), topic.Title)

		// 检索相关新闻
		searchStartTime := time.Now()
		relatedNews, err := o.newsRetriever.SearchRelatedNews(topic, 5)
		searchTime := time.Since(searchStartTime).Seconds()

		// 保存向量搜索记录
		svcCtx := svc.SvcCtx
		if svcCtx != nil {
			var resultURLs []string
			for _, news := range relatedNews {
				resultURLs = append(resultURLs, news.URL)
			}
			resultURLsJSON, _ := json.Marshal(resultURLs)

			searchRecord := model.VectorSearchRecord{
				TopicId:     topic.ID,
				TopicTitle:  topic.Title,
				SearchQuery: topic.Title + " " + topic.Description,
				ResultCount: len(relatedNews),
				ResultURLs:  string(resultURLsJSON),
				SearchTime:  searchTime,
				Status:      "success",
			}

			if err != nil {
				searchRecord.Status = "failed"
				searchRecord.ErrorMsg = err.Error()
			}

			if saveErr := svcCtx.VectorSearchModel.SaveVectorSearchRecord(searchRecord); saveErr != nil {
				log.Printf("保存向量搜索记录失败: %v", saveErr)
			} else {
				log.Printf("💾 向量搜索记录已保存: %d 个结果，耗时: %.2f秒", len(relatedNews), searchTime)
			}
		}

		if err != nil {
			log.Printf("⚠️ 话题 '%s' 相关新闻检索失败: %v", topic.Title, err)
			continue
		}

		// 转换新闻URL列表
		var newsURLs []string
		for _, news := range relatedNews {
			newsURLs = append(newsURLs, news.URL)
		}

		// 生成文章
		generateStartTime := time.Now()
		article, err := o.contentGen.GenerateArticle(topic, newsURLs)
		generateTime := time.Since(generateStartTime).Seconds()

		// 保存生成文章记录
		if svcCtx != nil {
			relatedNewsJSON, _ := json.Marshal(newsURLs)

			articleRecord := model.GeneratedArticleRecord{
				TopicId:      topic.ID,
				TopicTitle:   topic.Title,
				LLMModel:     o.getLLMModelName(),
				GenerateTime: generateTime,
				Status:       "success",
				RelatedNews:  string(relatedNewsJSON),
			}

			if err != nil {
				articleRecord.Status = "failed"
				articleRecord.ErrorMsg = err.Error()
			} else {
				articleRecord.ArticleTitle = article.Title
				articleRecord.ArticleId = article.Id
				articleRecord.WordCount = article.WordCount
			}

			if saveErr := svcCtx.GeneratedArticleModel.SaveGeneratedArticleRecord(articleRecord); saveErr != nil {
				log.Printf("保存生成文章记录失败: %v", saveErr)
			} else {
				log.Printf("💾 生成文章记录已保存: %s，耗时: %.2f秒", articleRecord.ArticleTitle, generateTime)
			}
		}

		if err != nil {
			log.Printf("⚠️ 话题 '%s' 文章生成失败: %v", topic.Title, err)
			continue
		}

		// 保存文章
		if err := o.articleStorage.SaveArticle(*article); err != nil {
			log.Printf("⚠️ 文章保存失败: %v", err)
			continue
		}

		allArticles = append(allArticles, *article)
		log.Printf("✅ 成功生成文章: %s (%d字)", article.Title, article.WordCount)
	}

	result.ArticlesCount = len(allArticles)
	log.Printf("✅ 共生成 %d 篇文章", len(allArticles))

	// 3. 发送到印象笔记
	if len(allArticles) > 0 {
		log.Println("📧 步骤3: 发送到印象笔记...")
		if err := o.notifier.SendToEvernote(allArticles); err != nil {
			log.Printf("⚠️ 发送到印象笔记失败: %v", err)
			result.Error = fmt.Sprintf("发送失败: %v", err)
		} else {
			log.Printf("✅ 成功发送 %d 篇文章到印象笔记", len(allArticles))
		}
	}

	// 4. 发送汇总报告
	result.Success = result.Error == ""
	result.Duration = time.Since(startTime).String()

	log.Println("📊 步骤4: 发送汇总报告...")
	if err := o.notifier.SendSummaryReport(result); err != nil {
		log.Printf("⚠️ 汇总报告发送失败: %v", err)
	}

	// 5. 导出生成的内容到文件
	if len(allArticles) > 0 {
		log.Println("📄 导出生成的内容到文件...")
		if err := o.exportArticlesToFiles(allArticles, topics); err != nil {
			log.Printf("⚠️ 文件导出失败: %v", err)
		} else {
			log.Println("✅ 内容已成功导出到文件")
		}
	}

	log.Printf("🎉 工作流执行完成! 话题: %d, 文章: %d, 耗时: %s",
		result.TopicsCount, result.ArticlesCount, result.Duration)

	return result
}

// RunCustomWorkflow 执行自定义工作流
func (o *Orchestrator) RunCustomWorkflow(topicLimit int, articleLimit int) WorkflowResult {
	startTime := time.Now()
	result := WorkflowResult{
		ExecutedAt: startTime,
		Success:    false,
	}

	log.Printf("🚀 开始执行自定义工作流 (话题限制: %d, 文章限制: %d)...", topicLimit, articleLimit)

	// 获取最新话题
	topics, err := o.topicAggregator.GetLatestTopics(topicLimit)
	if err != nil {
		result.Error = fmt.Sprintf("获取话题失败: %v", err)
		result.Duration = time.Since(startTime).String()
		return result
	}
	result.TopicsCount = len(topics)

	// 生成文章（限制数量）
	articles, err := o.contentGen.GenerateArticles(topics[:min(len(topics), articleLimit)])
	if err != nil {
		result.Error = fmt.Sprintf("文章生成失败: %v", err)
		result.Duration = time.Since(startTime).String()
		return result
	}
	result.ArticlesCount = len(articles)

	// 保存文章
	for _, article := range articles {
		if err := o.articleStorage.SaveArticle(article); err != nil {
			log.Printf("⚠️ 文章保存失败: %v", err)
		}
	}

	result.Success = true
	result.Duration = time.Since(startTime).String()

	log.Printf("🎉 自定义工作流完成! 话题: %d, 文章: %d, 耗时: %s",
		result.TopicsCount, result.ArticlesCount, result.Duration)

	return result
}

// exportArticlesToFiles 导出文章到多种格式的文件
func (o *Orchestrator) exportArticlesToFiles(articles []Article, topics []Topic) error {
	if len(articles) == 0 {
		return fmt.Errorf("没有文章需要导出")
	}

	// 创建输出目录
	outputDir := "output"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}

	timestamp := time.Now().Format("20060102_150405")

	// 导出文章为JSON
	articlesFile := fmt.Sprintf("%s/articles_%s.json", outputDir, timestamp)
	if err := o.exportArticlesToJSON(articles, articlesFile); err != nil {
		return fmt.Errorf("导出文章JSON失败: %v", err)
	}

	// 导出话题为JSON
	if len(topics) > 0 {
		topicsFile := fmt.Sprintf("%s/topics_%s.json", outputDir, timestamp)
		if err := o.exportTopicsToJSON(topics, topicsFile); err != nil {
			return fmt.Errorf("导出话题JSON失败: %v", err)
		}
	}

	// 导出文章为Markdown格式
	markdownFile := fmt.Sprintf("%s/articles_%s.md", outputDir, timestamp)
	if err := o.exportArticlesToMarkdown(articles, markdownFile); err != nil {
		return fmt.Errorf("导出Markdown失败: %v", err)
	}

	log.Printf("✅ 文章已导出到以下文件:")
	log.Printf("   JSON: %s", articlesFile)
	log.Printf("   Markdown: %s", markdownFile)
	if len(topics) > 0 {
		log.Printf("   Topics JSON: %s/topics_%s.json", outputDir, timestamp)
	}

	return nil
}

// exportArticlesToJSON 导出文章为JSON格式
func (o *Orchestrator) exportArticlesToJSON(articles []Article, filename string) error {
	data, err := json.MarshalIndent(articles, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}

	return os.WriteFile(filename, data, 0644)
}

// exportTopicsToJSON 导出话题为JSON格式
func (o *Orchestrator) exportTopicsToJSON(topics []Topic, filename string) error {
	data, err := json.MarshalIndent(topics, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}

	return os.WriteFile(filename, data, 0644)
}

// exportArticlesToMarkdown 导出文章为Markdown格式
func (o *Orchestrator) exportArticlesToMarkdown(articles []Article, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 写入标题
	fmt.Fprintf(file, "# 生成的新闻文章\n\n")
	fmt.Fprintf(file, "生成时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Fprintf(file, "文章数量: %d\n\n", len(articles))
	fmt.Fprintf(file, "---\n\n")

	// 写入每篇文章
	for i, article := range articles {
		fmt.Fprintf(file, "## %d. %s\n\n", i+1, article.Title)
		fmt.Fprintf(file, "**创建时间:** %s  \n", article.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Fprintf(file, "**字数:** %d  \n", article.WordCount)
		fmt.Fprintf(file, "**状态:** %s  \n\n", article.Status)

		if article.Summary != "" {
			fmt.Fprintf(file, "### 摘要\n\n%s\n\n", article.Summary)
		}

		fmt.Fprintf(file, "### 正文\n\n%s\n\n", article.Content)
		fmt.Fprintf(file, "---\n\n")
	}

	return nil
}

// GetSystemStatus 获取系统状态
func (o *Orchestrator) GetSystemStatus() map[string]interface{} {
	status := make(map[string]interface{})

	// 获取最近的话题数量
	topics, _ := o.topicStorage.GetTopics(10)
	status["recent_topics_count"] = len(topics)

	// 获取最近的文章数量
	articles, _ := o.articleStorage.GetArticles(10)
	status["recent_articles_count"] = len(articles)

	// 获取待审核文章数量
	draftArticles, _ := o.articleStorage.GetArticlesByStatus("draft")
	status["draft_articles_count"] = len(draftArticles)

	status["last_check"] = time.Now()
	status["system_healthy"] = true

	return status
}

// getLLMModelName 获取LLM模型名称
func (o *Orchestrator) getLLMModelName() string {
	// 根据ContentGenerator类型返回模型名称
	switch o.contentGen.(type) {
	default:
		return "Unknown"
	}
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
