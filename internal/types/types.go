package types

import (
	"time"
)

type StreamResponse struct {
	Id      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   *Usage   `json:"usage"` // 可能为 null
}

type Choice struct {
	Index        int     `json:"index"`
	Delta        Delta   `json:"delta"`
	FinishReason *string `json:"finish_reason"` // 可能为 null 或 "stop"
}

type Delta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	TotalTokens      int `json:"total_tokens"`
	CompletionTokens int `json:"completion_tokens"`
}

// 新建阿里百练请求结构体
// AliDeepSeekResponse 阿里云百炼平台的响应结构体
type AliDeepSeekResponse struct {
	Choices []struct {
		Delta struct {
			Content          string `json:"content,omitempty"`
			ReasoningContent string `json:"reasoning_content"`
		} `json:"delta"`
		FinishReason string  `json:"finish_reason,omitempty"`
		Index        int     `json:"index"`
		Logprobs     float64 `json:"logprobs,omitempty"`
	} `json:"choices"`
	Object            string `json:"object"`
	Usage             Usage  `json:"usage,omitempty"`
	Created           int64  `json:"created"`
	SystemFingerprint string `json:"system_fingerprint,omitempty"`
	Model             string `json:"model"`
	Id                string `json:"id"`
}

// openRouter请求结构体
// OpenRouterResponse openRouter的响应结构体
type OpenRouterResponse struct {
	ID       string       `json:"id"`
	Provider string       `json:"provider"`
	Model    string       `json:"model"`
	Object   string       `json:"object"`
	Created  int64        `json:"created"`
	Choices  []OpenChoice `json:"choices"`
}

type OpenChoice struct {
	Index              int       `json:"index"`
	Delta              OpenDelta `json:"delta"`
	FinishReason       *string   `json:"finish_reason,omitempty"`
	NativeFinishReason *string   `json:"native_finish_reason,omitempty"`
	Logprobs           *float64  `json:"logprobs,omitempty"`
}

type OpenDelta struct {
	Role      string `json:"role"`
	Content   string `json:"content"`
	Reasoning string `json:"reasoning"`
}

// HistoryMessage 历史消息内容结构体
type HistoryMessage struct {
	Role    string `json:"role"`    // 消息角色
	Content string `json:"content"` // 消息内容
}

// ai调用外部api结构体
type AiNetwork struct {
	FuncName string `json:"func_name"`
	Content  string `json:"content"`
}

type AiToolCalls struct {
	Name    string                   `json:"name"`
	Content []map[string]interface{} `json:"content"`
}

type AiRagCall struct {
	RagType string                   `json:"name"`
	Content []map[string]interface{} `json:"content"`
}

// Request 表示请求的主结构体
type Request struct {
	// 需要 "messages" 或 "prompt" 其中之一
	Messages []Message `json:"messages,omitempty"` // 用户消息列表
	Prompt   string    `json:"prompt,omitempty"`   // 提示字符串

	// 如果未指定 "model"，则使用用户的默认模型
	Model string `json:"model,omitempty"` // 使用的模型名称

	// 允许强制模型产生特定的输出格式
	ResponseFormat *ResponseFormat `json:"response_format,omitempty"` // 响应格式类型

	Stop   interface{} `json:"stop,omitempty"`   // 停止的字符或字符数组
	Stream bool        `json:"stream,omitempty"` // 启用流式处理

	// LLM 参数设置
	MaxTokens   int        `json:"max_tokens,omitempty"`  // 最大令牌数
	Temperature float64    `json:"temperature,omitempty"` // 温度范围：0-2
	Tools       []Tool     `json:"tools,omitempty"`       // 使用的工具
	ToolChoice  ToolChoice `json:"tool_choice,omitempty"` // 工具选择

	// 高级可选参数
	Seed              int         `json:"seed,omitempty"`               // 随机种子
	TopP              float64     `json:"top_p,omitempty"`              // Top-p 参数
	TopK              int         `json:"top_k,omitempty"`              // Top-k 参数
	FrequencyPenalty  float64     `json:"frequency_penalty,omitempty"`  // 频率惩罚
	PresencePenalty   float64     `json:"presence_penalty,omitempty"`   // 出现惩罚
	RepetitionPenalty float64     `json:"repetition_penalty,omitempty"` // 重复惩罚
	LogitBias         map[int]int `json:"logit_bias,omitempty"`         // Logit 偏差
	TopLogprobs       int         `json:"top_logprobs"`                 // Top logprobs 数量
	MinP              float64     `json:"min_p,omitempty"`              // 最小概率
	TopA              float64     `json:"top_a,omitempty"`              // Top-a 参数

	// 为模型提供预测输出以减少延迟
	Prediction *Prediction `json:"prediction,omitempty"` // 预测的内容

	// OpenRouter 仅适用的参数
	Transforms []string             `json:"transforms,omitempty"` // 转换
	Models     []string             `json:"models,omitempty"`     // 模型列表
	Route      string               `json:"route,omitempty"`      // 路由选项
	Provider   *ProviderPreferences `json:"provider,omitempty"`   // 提供商偏好设置
}

// TextContent 表示文本内容
type TextContent struct {
	Type string `json:"type"` // 内容类型，固定为 "text"
	Text string `json:"text"` // 文本内容
}

// ImageContentPart 表示图像内容部分
type ImageContentPart struct {
	Type     string `json:"type"` // 内容类型，固定为 "image_url"
	ImageURL struct {
		URL    string `json:"url"`    // 图像的 URL 或 base64 编码的图像数据
		Detail string `json:"detail"` // 可选，默认值为 "auto"
	} `json:"image_url"` // 图像的 URL 或 base64 编码的图像数据
}

// ContentPart 表示文本或图像内容的联合类型
type ContentPart struct {
	TextContent      TextContent      `json:"text_content,omitempty"`
	ImageContentPart ImageContentPart `json:"image_content_part,omitempty"`
}

// Message 表示消息
type Message struct {
	Role       string      `json:"role"`                   // 消息的角色 ("user" | "assistant" | "system")
	Content    interface{} `json:"content"`                // 内容，可能是字符串或内容部分数组
	Name       string      `json:"name,omitempty"`         // 可选，非 OpenAI 模型时，使用 "{name}: {content}" 格式
	ToolCallID string      `json:"tool_call_id,omitempty"` // 工具调用 ID，仅当角色为 "tool" 时使用
}

// FunctionDescription 表示函数描述，用于工具的功能定义
type FunctionDescription struct {
	Description string      `json:"description,omitempty"` // 描述
	Name        string      `json:"name"`                  // 函数名称
	Parameters  interface{} `json:"parameters"`            // 函数参数（JSON Schema 对象）
}

// Tool 表示工具，包含函数定义
type Tool struct {
	Type     string              `json:"type"`     // 工具类型，固定为 "function"
	Function FunctionDescription `json:"function"` // 函数描述
}

// ToolChoice 表示工具选择
type ToolChoice struct {
	Type     string `json:"type"` // 类型，"none" | "auto" | "function"
	Function struct {
		Name string `json:"name"` // 函数名称
	} `json:"function,omitempty"`
}

// ProviderPreferences 表示提供商偏好设置
type ProviderPreferences struct {
	// 可以在这里定义提供商的相关设置
}

// ResponseFormat 表示响应格式的定义
type ResponseFormat struct {
	Type string `json:"type"` // 输出类型
}

// Prediction 表示预测的内容结构
type Prediction struct {
	Type    string `json:"type"`    // 内容类型
	Content string `json:"content"` // 预测的内容
}
type ApiConfig struct {
	Url       string `json:"url"`
	SecretKey string `json:"secretKey"`
}

// WebSearchResult 网络搜索结果结构体
type WebSearchResult struct {
	Index          int       `json:"index"`           // 结果序号
	Domain         string    `json:"domain"`          // 来源域名
	URL            string    `json:"url"`             // 结果链接
	Title          string    `json:"title"`           // 结果标题
	Summary        string    `json:"summary"`         // 原始摘要
	Timestamp      time.Time `json:"timestamp"`       // 抓取时间戳
	TimeMarker     string    `json:"time_marker"`     // 相对时间标记
	PublishTime    time.Time `json:"publish_time"`    // 内容发布时间
	CleanedSummary string    `json:"cleaned_summary"` // 清洗后摘要
}

type FetchResponse struct {
	Content string `json:"content"`
	Prefix  string `json:"prefix"`
}

// Fetch 结构体定义
type Fetch struct {
	URL        string `json:"url"`
	MaxLength  int    `json:"max_length"`
	StartIndex int    `json:"start_index"`
	Raw        bool   `json:"raw"`
}

// Fetch 功能参数
type FetchParams struct {
	URL      string
	IsManual bool
	Raw      bool
}

// HotItem 热榜项目
type HotItem struct {
	Title       string    `json:"title"`
	Description string    `json:"description"`
	URL         string    `json:"url"`
	HotValue    float64   `json:"hot_value"`
	Platform    string    `json:"platform"`
	Rank        int       `json:"rank"`
	CreatedAt   time.Time `json:"created_at"`
}

// Topic 话题结构
type Topic struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	HotValue    float64   `json:"hot_value"`
	Categories  []string  `json:"categories"`
	Platform    string    `json:"platform"`
	Vector      []float32 `json:"-"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Article 生成的文章结构
type Article struct {
	Id          string    `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Summary     string    `json:"summary"`
	TopicId     string    `json:"topic_id"`
	RelatedNews []string  `json:"related_news"` // 相关新闻URL列表
	WordCount   int       `json:"word_count"`
	CreatedAt   time.Time `json:"created_at"`
	Status      string    `json:"status"` // draft, reviewed, published
}
