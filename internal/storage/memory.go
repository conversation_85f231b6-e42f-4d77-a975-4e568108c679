package storage

import (
	"fmt"
	"sort"
	"sync"
	"time"

	"newsBot/internal/core"
)

// MemoryTopicStorage 内存话题存储
type MemoryTopicStorage struct {
	topics map[string]core.Topic
	mutex  sync.RWMutex
}

// NewMemoryTopicStorage 创建新的内存话题存储
func NewMemoryTopicStorage() *MemoryTopicStorage {
	return &MemoryTopicStorage{
		topics: make(map[string]core.Topic),
	}
}

func (m *MemoryTopicStorage) SaveTopics(topics []core.Topic) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, topic := range topics {
		if topic.ID == "" {
			topic.ID = fmt.Sprintf("topic_%d", time.Now().UnixNano())
		}
		topic.UpdatedAt = time.Now()
		m.topics[topic.ID] = topic
	}

	return nil
}

func (m *MemoryTopicStorage) GetTopics(limit int) ([]core.Topic, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var topics []core.Topic
	for _, topic := range m.topics {
		topics = append(topics, topic)
	}

	// 按创建时间排序（最新的在前）
	sort.Slice(topics, func(i, j int) bool {
		return topics[i].CreatedAt.After(topics[j].CreatedAt)
	})

	if limit > 0 && len(topics) > limit {
		topics = topics[:limit]
	}

	return topics, nil
}

func (m *MemoryTopicStorage) GetTopicByID(id string) (*core.Topic, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	topic, exists := m.topics[id]
	if !exists {
		return nil, fmt.Errorf("话题不存在: %s", id)
	}

	return &topic, nil
}

func (m *MemoryTopicStorage) UpdateTopic(topic core.Topic) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.topics[topic.ID]; !exists {
		return fmt.Errorf("话题不存在: %s", topic.ID)
	}

	topic.UpdatedAt = time.Now()
	m.topics[topic.ID] = topic
	return nil
}

func (m *MemoryTopicStorage) DeleteTopic(id string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.topics[id]; !exists {
		return fmt.Errorf("话题不存在: %s", id)
	}

	delete(m.topics, id)
	return nil
}

// MemoryArticleStorage 内存文章存储
type MemoryArticleStorage struct {
	articles map[string]core.Article
	mutex    sync.RWMutex
}

// NewMemoryArticleStorage 创建新的内存文章存储
func NewMemoryArticleStorage() *MemoryArticleStorage {
	return &MemoryArticleStorage{
		articles: make(map[string]core.Article),
	}
}

func (m *MemoryArticleStorage) SaveArticle(article core.Article) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if article.Id == "" {
		article.Id = fmt.Sprintf("article_%d", time.Now().UnixNano())
	}

	m.articles[article.Id] = article
	return nil
}

func (m *MemoryArticleStorage) GetArticles(limit int) ([]core.Article, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var articles []core.Article
	for _, article := range m.articles {
		articles = append(articles, article)
	}

	// 按创建时间排序（最新的在前）
	sort.Slice(articles, func(i, j int) bool {
		return articles[i].CreatedAt.After(articles[j].CreatedAt)
	})

	if limit > 0 && len(articles) > limit {
		articles = articles[:limit]
	}

	return articles, nil
}

func (m *MemoryArticleStorage) GetArticleByID(id string) (*core.Article, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	article, exists := m.articles[id]
	if !exists {
		return nil, fmt.Errorf("文章不存在: %s", id)
	}

	return &article, nil
}

func (m *MemoryArticleStorage) UpdateArticle(article core.Article) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.articles[article.Id]; !exists {
		return fmt.Errorf("文章不存在: %s", article.Id)
	}

	m.articles[article.Id] = article
	return nil
}

func (m *MemoryArticleStorage) DeleteArticle(id string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.articles[id]; !exists {
		return fmt.Errorf("文章不存在: %s", id)
	}

	delete(m.articles, id)
	return nil
}

func (m *MemoryArticleStorage) GetArticlesByStatus(status string) ([]core.Article, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var articles []core.Article
	for _, article := range m.articles {
		if article.Status == status {
			articles = append(articles, article)
		}
	}

	// 按创建时间排序（最新的在前）
	sort.Slice(articles, func(i, j int) bool {
		return articles[i].CreatedAt.After(articles[j].CreatedAt)
	})

	return articles, nil
}
