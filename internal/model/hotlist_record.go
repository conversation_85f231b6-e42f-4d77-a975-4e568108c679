package model

import (
	"time"

	"gorm.io/gorm"
	"newsBot/internal/types"
)

// HotlistRecord 热搜记录
type HotlistRecord struct {
	Id          int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	SessionId   int64     `gorm:"column:session_id;not null;comment:会话ID" json:"session_id"`
	Platform    string    `gorm:"column:platform;not null;comment:平台名称" json:"platform"`
	Title       string    `gorm:"column:title;not null;comment:标题" json:"title"`
	Description string    `gorm:"column:description;comment:描述" json:"description"`
	URL         string    `gorm:"column:url;comment:链接" json:"url"`
	HotValue    float64   `gorm:"column:hot_value;default:0;comment:热度值" json:"hot_value"`
	Rank        int       `gorm:"column:rank;default:0;comment:排名" json:"rank"`
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (HotlistRecord) TableName() string {
	return "hotlist_records"
}

// HotlistRecordModel 热搜记录模型
type HotlistRecordModel struct {
	db *gorm.DB
}

// NewHotlistRecordModel 创建热搜记录模型
func NewHotlistRecordModel(db *gorm.DB) *HotlistRecordModel {
	return &HotlistRecordModel{
		db: db,
	}
}

// Create 创建热搜记录
func (m *HotlistRecordModel) Create(record *HotlistRecord) error {
	return m.db.Create(record).Error
}

// SaveHotlistRecord 保存热搜记录
func (m *HotlistRecordModel) SaveHotlistRecord(sessionID int64, item types.HotItem) error {
	record := &HotlistRecord{
		SessionId:   sessionID,
		Platform:    item.Platform,
		Title:       item.Title,
		Description: item.Description,
		URL:         item.URL,
		HotValue:    item.HotValue,
		Rank:        item.Rank,
		CreatedAt:   time.Now(),
	}

	return m.db.Create(record).Error
}

// GetHotlistRecordsBySession 根据会话ID获取热搜记录
func (m *HotlistRecordModel) GetHotlistRecordsBySession(sessionID int64, limit int) ([]*HotlistRecord, error) {
	var records []*HotlistRecord

	db := m.db.Where("session_id = ?", sessionID).Order("rank ASC, hot_value DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetById 根据ID获取热搜记录
func (m *HotlistRecordModel) GetById(id int64) (*HotlistRecord, error) {
	var record HotlistRecord
	if err := m.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// List 获取热搜记录列表（带分页和条件）
func (m *HotlistRecordModel) List(page, pageSize int, conditions map[string]interface{}) ([]*HotlistRecord, int64, error) {
	var records []*HotlistRecord
	var total int64

	db := m.db.Model(&HotlistRecord{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "platform" || key == "title" || key == "description" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("rank ASC, hot_value DESC")
	if err := db.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetHotlistStats 获取热搜统计信息
func (m *HotlistRecordModel) GetHotlistStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总体统计
	var totalItems int64
	var totalSessions int64
	var avgHotValue float64

	// 获取热搜记录总数和平均热度值
	if err := m.db.Model(&HotlistRecord{}).Count(&totalItems).Error; err != nil {
		return nil, err
	}

	if err := m.db.Model(&HotlistRecord{}).Select("AVG(hot_value)").Row().Scan(&avgHotValue); err != nil {
		avgHotValue = 0.0
	}

	// 获取会话总数 - 需要访问HotlistSessionModel
	// 这里我们直接查询数据库
	if err := m.db.Model(&HotlistSession{}).Count(&totalSessions).Error; err != nil {
		return nil, err
	}

	stats["total_items"] = totalItems
	stats["total_sessions"] = totalSessions
	stats["avg_hot_value"] = avgHotValue

	// 按平台统计
	type PlatformStat struct {
		Platform    string  `json:"platform"`
		Count       int64   `json:"count"`
		AvgHotValue float64 `json:"avg_hot_value"`
		MaxHotValue float64 `json:"max_hot_value"`
	}

	var platformStats []PlatformStat
	if err := m.db.Model(&HotlistRecord{}).
		Select("platform, COUNT(*) as count, AVG(hot_value) as avg_hot_value, MAX(hot_value) as max_hot_value").
		Group("platform").
		Order("count DESC").
		Scan(&platformStats).Error; err != nil {
		return nil, err
	}

	// 转换为map格式
	var platformStatsMap []map[string]interface{}
	for _, stat := range platformStats {
		platformStatsMap = append(platformStatsMap, map[string]interface{}{
			"platform":      stat.Platform,
			"count":         stat.Count,
			"avg_hot_value": stat.AvgHotValue,
			"max_hot_value": stat.MaxHotValue,
		})
	}

	stats["platform_stats"] = platformStatsMap

	// 最近7天的趋势
	type TrendStat struct {
		Date        string  `json:"date"`
		Count       int64   `json:"count"`
		AvgHotValue float64 `json:"avg_hot_value"`
	}

	var trendStats []TrendStat
	if err := m.db.Model(&HotlistRecord{}).
		Select("DATE(created_at) as date, COUNT(*) as count, AVG(hot_value) as avg_hot_value").
		Where("created_at >= datetime('now', '-7 days')").
		Group("DATE(created_at)").
		Order("date DESC").
		Scan(&trendStats).Error; err != nil {
		return nil, err
	}

	// 转换为map格式
	var trendStatsMap []map[string]interface{}
	for _, stat := range trendStats {
		trendStatsMap = append(trendStatsMap, map[string]interface{}{
			"date":          stat.Date,
			"count":         stat.Count,
			"avg_hot_value": stat.AvgHotValue,
		})
	}

	stats["trend_stats"] = trendStatsMap

	return stats, nil
}
