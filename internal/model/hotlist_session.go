package model

import (
	"time"

	"gorm.io/gorm"
)

// HotlistSession 热搜会话记录
type HotlistSession struct {
	Id           int64      `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	StartTime    time.Time  `gorm:"column:start_time;not null;comment:开始时间" json:"start_time"`
	EndTime      *time.Time `gorm:"column:end_time;comment:结束时间" json:"end_time,omitempty"`
	TotalItems   int        `gorm:"column:total_items;default:0;comment:总项目数" json:"total_items"`
	SuccessItems int        `gorm:"column:success_items;default:0;comment:成功项目数" json:"success_items"`
	Status       string     `gorm:"column:status;not null;default:'running';comment:状态" json:"status"` // running, completed, failed
	ErrorMsg     string     `gorm:"column:error_msg;comment:错误信息" json:"error_msg,omitempty"`
	CreatedAt    time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (HotlistSession) TableName() string {
	return "hotlist_sessions"
}

// HotlistSessionModel 热搜会话模型
type HotlistSessionModel struct {
	db *gorm.DB
}

// NewHotlistSessionModel 创建热搜会话模型
func NewHotlistSessionModel(db *gorm.DB) *HotlistSessionModel {
	return &HotlistSessionModel{
		db: db,
	}
}

// Create 创建热搜会话
func (m *HotlistSessionModel) Create(session *HotlistSession) error {
	return m.db.Create(session).Error
}

// StartHotlistSession 开始新的热搜会话
func (m *HotlistSessionModel) StartHotlistSession() (int64, error) {
	session := &HotlistSession{
		StartTime: time.Now(),
		Status:    "running",
	}

	if err := m.db.Create(session).Error; err != nil {
		return 0, err
	}

	return session.ID, nil
}

// EndHotlistSession 结束热搜会话
func (m *HotlistSessionModel) EndHotlistSession(sessionID int64, totalItems, successItems int, status string, errorMsg string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"end_time":      &now,
		"total_items":   totalItems,
		"success_items": successItems,
		"status":        status,
		"error_msg":     errorMsg,
	}

	return m.db.Model(&HotlistSession{}).Where("id = ?", sessionID).Updates(updates).Error
}

// GetHotlistSessions 获取热搜会话列表
func (m *HotlistSessionModel) GetHotlistSessions(limit int) ([]*HotlistSession, error) {
	var sessions []*HotlistSession

	db := m.db.Order("start_time DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&sessions).Error; err != nil {
		return nil, err
	}

	return sessions, nil
}

// GetById 根据ID获取热搜会话
func (m *HotlistSessionModel) GetById(id int64) (*HotlistSession, error) {
	var session HotlistSession
	if err := m.db.First(&session, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// List 获取热搜会话列表（带分页和条件）
func (m *HotlistSessionModel) List(page, pageSize int, conditions map[string]interface{}) ([]*HotlistSession, int64, error) {
	var sessions []*HotlistSession
	var total int64

	db := m.db.Model(&HotlistSession{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "status" {
				db = db.Where(key+" = ?", value)
			} else {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("start_time DESC")
	if err := db.Find(&sessions).Error; err != nil {
		return nil, 0, err
	}

	return sessions, total, nil
}
