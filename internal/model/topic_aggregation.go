package model

import (
	"time"

	"gorm.io/gorm"
)

// TopicAggregationRecord LLM话题合并记录
type TopicAggregationRecord struct {
	Id              int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	SessionId       int64     `gorm:"column:session_id;not null;comment:会话ID" json:"session_id"`
	InputItemsCount int       `gorm:"column:input_items_count;not null;comment:输入项目数量" json:"input_items_count"`
	OutputTopics    string    `gorm:"column:output_topics;not null;comment:输出话题JSON" json:"output_topics"` // JSON格式存储话题列表
	LLMModel        string    `gorm:"column:llm_model;not null;comment:LLM模型" json:"llm_model"`
	ProcessTime     float64   `gorm:"column:process_time;default:0;comment:处理时间" json:"process_time"` // 处理时间（秒）
	Status          string    `gorm:"column:status;not null;default:'success';comment:状态" json:"status"`       // success, failed
	ErrorMsg        string    `gorm:"column:error_msg;comment:错误信息" json:"error_msg,omitempty"`
	CreatedAt       time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (TopicAggregationRecord) TableName() string {
	return "topic_aggregation_records"
}

// TopicAggregationModel 话题合并记录模型
type TopicAggregationModel struct {
	db *gorm.DB
}

// NewTopicAggregationModel 创建话题合并记录模型
func NewTopicAggregationModel(db *gorm.DB) *TopicAggregationModel {
	return &TopicAggregationModel{
		db: db,
	}
}

// Create 创建话题合并记录
func (m *TopicAggregationModel) Create(record *TopicAggregationRecord) error {
	return m.db.Create(record).Error
}

// SaveTopicAggregationRecord 保存话题合并记录
func (m *TopicAggregationModel) SaveTopicAggregationRecord(record TopicAggregationRecord) error {
	record.CreatedAt = time.Now()
	return m.db.Create(&record).Error
}

// GetTopicAggregationRecords 获取话题合并记录
func (m *TopicAggregationModel) GetTopicAggregationRecords(limit int) ([]*TopicAggregationRecord, error) {
	var records []*TopicAggregationRecord

	db := m.db.Order("created_at DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetById 根据ID获取话题合并记录
func (m *TopicAggregationModel) GetById(id int64) (*TopicAggregationRecord, error) {
	var record TopicAggregationRecord
	if err := m.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// List 获取话题合并记录列表（带分页和条件）
func (m *TopicAggregationModel) List(page, pageSize int, conditions map[string]interface{}) ([]*TopicAggregationRecord, int64, error) {
	var records []*TopicAggregationRecord
	var total int64

	db := m.db.Model(&TopicAggregationRecord{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "llm_model" || key == "status" {
				db = db.Where(key+" = ?", value)
			} else {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("created_at DESC")
	if err := db.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
