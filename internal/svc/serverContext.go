package svc

import (
	"os"
	"path/filepath"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"log"
	"newsBot/internal/config"
	"newsBot/internal/model"
)

var SvcCtx *ServiceContext

func init() {
	SvcCtx = NewServiceContext(config.Config{})
}

type ServiceContext struct {
	Config                config.Config
	DB                    *gorm.DB
	ProxyModel            *model.ProxyModel
	CrawlSessionModel     *model.CrawlSessionModel
	NewsRecordModel       *model.NewsRecordModel
	HotlistSessionModel   *model.HotlistSessionModel
	HotlistRecordModel    *model.HotlistRecordModel
	TopicAggregationModel *model.TopicAggregationModel
	VectorSearchModel     *model.VectorSearchModel
	GeneratedArticleModel *model.GeneratedArticleModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	// 确保数据目录存在
	dataDir := "data"
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		log.Fatalln("创建数据目录失败", "error", err.Error())
	}

	// 数据库文件路径
	dbPath := filepath.Join(dataDir, "newsbot_gorm.db")

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Fatalln("连接数据库失败", "error", err.Error())
	}

	// 自动迁移数据表
	if err = autoMigrate(db); err != nil {
		log.Fatalln("数据表迁移失败", "error", err.Error())
	}

	return &ServiceContext{
		Config:                c,
		DB:                    db,
		ProxyModel:            model.NewProxyModel(db),
		CrawlSessionModel:     model.NewCrawlSessionModel(db),
		NewsRecordModel:       model.NewNewsRecordModel(db),
		HotlistSessionModel:   model.NewHotlistSessionModel(db),
		HotlistRecordModel:    model.NewHotlistRecordModel(db),
		TopicAggregationModel: model.NewTopicAggregationModel(db),
		VectorSearchModel:     model.NewVectorSearchModel(db),
		GeneratedArticleModel: model.NewGeneratedArticleModel(db),
	}
}

// 自动迁移数据表结构
func autoMigrate(db *gorm.DB) error {
	// 自动迁移所有模型
	return db.AutoMigrate(
		&model.Proxy{},
		&model.CrawlSession{},
		&model.NewsRecord{},
		&model.HotlistSession{},
		&model.HotlistRecord{},
		&model.TopicAggregationRecord{},
		&model.VectorSearchRecord{},
		&model.GeneratedArticleRecord{},
	)
}
