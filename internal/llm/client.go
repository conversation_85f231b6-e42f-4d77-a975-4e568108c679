package llm

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"newsBot/internal/constant"
	"newsBot/internal/core"
)

// DashScopeLLMClient 通义千问LLM客户端
type DashScopeLLMClient struct {
	APIKey   string
	Endpoint string
	Client   *http.Client
}

// LLMRequest LLM请求结构
type LLMRequest struct {
	Model string `json:"model"`
	Input struct {
		Messages []Message `json:"messages"`
	} `json:"input"`
	Parameters struct {
		Temperature float64 `json:"temperature"`
		MaxTokens   int     `json:"max_tokens"`
	} `json:"parameters"`
}

// Message 消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// LLMResponse LLM响应结构
type LLMResponse struct {
	Output struct {
		Text string `json:"text"`
	} `json:"output"`
	Usage struct {
		TotalTokens int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// NewDashScopeLLMClient 创建新的LLM客户端
func NewDashScopeLLMClient() *DashScopeLLMClient {
	apiKey := os.Getenv("DASHSCOPE_API_KEY")
	if apiKey == "" {
		panic("DASHSCOPE_API_KEY environment variable is required")
	}

	endpoint := os.Getenv("LLM_ENDPOINT")
	if endpoint == "" {
		endpoint = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
	}

	return &DashScopeLLMClient{
		APIKey:   apiKey,
		Endpoint: endpoint,
		Client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// Aggregate 聚合热榜项目为话题
func (c *DashScopeLLMClient) Aggregate(items []core.HotItem) ([]core.Topic, error) {
	if len(items) == 0 {
		return nil, fmt.Errorf("没有热榜项目需要聚合")
	}

	// 构建聚合提示词
	prompt := c.buildAggregationPrompt(items)

	// 调用LLM
	response, err := c.callLLM(prompt)
	if err != nil {
		return nil, fmt.Errorf("LLM调用失败: %v", err)
	}

	// 解析响应为话题
	topics, err := c.parseTopicsFromResponse(response)
	if err != nil {
		return nil, fmt.Errorf("话题解析失败: %v", err)
	}

	return topics, nil
}

// GenerateArticle 生成文章
func (c *DashScopeLLMClient) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	// 构建文章生成提示词
	prompt := c.buildArticlePrompt(topic, relatedNews)

	// 调用LLM
	response, err := c.callLLM(prompt)
	if err != nil {
		return nil, fmt.Errorf("文章生成失败: %v", err)
	}

	// 解析文章
	article, err := c.parseArticleFromResponse(response, topic)
	if err != nil {
		return nil, fmt.Errorf("文章解析失败: %v", err)
	}

	return article, nil
}

// GenerateSummary 生成摘要
func (c *DashScopeLLMClient) GenerateSummary(content string) (string, error) {
	prompt := fmt.Sprintf(`请为以下内容生成一个简洁的摘要（不超过200字）：

%s

摘要：`, content)

	response, err := c.callLLM(prompt)
	if err != nil {
		return "", fmt.Errorf("摘要生成失败: %v", err)
	}

	return strings.TrimSpace(response), nil
}

// FireworksLLMClient Fireworks AI LLM客户端
type FireworksLLMClient struct {
	APIKey   string
	Endpoint string
	Client   *http.Client
}

// FireworksRequest Fireworks AI请求结构
type FireworksRequest struct {
	Model       string             `json:"model"`
	Messages    []FireworksMessage `json:"messages"`
	MaxTokens   int                `json:"max_tokens"`
	Temperature float64            `json:"temperature"`
	Stream      bool               `json:"stream"`
}

// FireworksMessage Fireworks AI消息结构
type FireworksMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// FireworksResponse Fireworks AI响应结构
type FireworksResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		TotalTokens int `json:"total_tokens"`
	} `json:"usage"`
}

// NewFireworksLLMClient 创建新的Fireworks LLM客户端
func NewFireworksLLMClient() *FireworksLLMClient {
	// 从常量配置中获取API信息
	apiConfig := constant.ApiConfigMap()[constant.ApiPlatform_Firework]

	return &FireworksLLMClient{
		APIKey:   apiConfig.SecretKey,
		Endpoint: apiConfig.Url,
		Client: &http.Client{
			Timeout: 120 * time.Second, // 增加超时时间，因为LLM响应可能较慢
		},
	}
}

// Aggregate 聚合热榜项目为话题
func (c *FireworksLLMClient) Aggregate(items []core.HotItem) ([]core.Topic, error) {
	if len(items) == 0 {
		return nil, fmt.Errorf("没有热榜项目需要聚合")
	}

	// 构建聚合提示词
	prompt := c.buildAggregationPrompt(items)

	// 调用LLM
	response, err := c.callFireworksLLM(prompt)
	if err != nil {
		return nil, fmt.Errorf("Fireworks LLM调用失败: %v", err)
	}

	// 解析响应为话题
	topics, err := c.parseTopicsFromResponse(response)
	if err != nil {
		return nil, fmt.Errorf("话题解析失败: %v", err)
	}

	return topics, nil
}

// GenerateArticle 生成文章
func (c *FireworksLLMClient) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	// 构建文章生成提示词
	prompt := c.buildArticlePrompt(topic, relatedNews)

	// 调用LLM
	response, err := c.callFireworksLLM(prompt)
	if err != nil {
		return nil, fmt.Errorf("文章生成失败: %v", err)
	}

	// 解析文章
	article, err := c.parseArticleFromResponse(response, topic)
	if err != nil {
		return nil, fmt.Errorf("文章解析失败: %v", err)
	}

	return article, nil
}

// GenerateSummary 生成摘要
func (c *FireworksLLMClient) GenerateSummary(content string) (string, error) {
	prompt := fmt.Sprintf(`请为以下内容生成一个简洁的摘要（不超过200字）：

%s

摘要：`, content)

	response, err := c.callFireworksLLM(prompt)
	if err != nil {
		return "", fmt.Errorf("摘要生成失败: %v", err)
	}

	return strings.TrimSpace(response), nil
}

// callFireworksLLM 调用Fireworks LLM API
func (c *FireworksLLMClient) callFireworksLLM(prompt string) (string, error) {
	// 获取模型映射
	modelMap := constant.GetModelMap()
	fireworksModels := modelMap[constant.ApiPlatform_Firework]

	// 使用 llama-3.1-70b 模型，适合复杂的推理任务
	model := "accounts/fireworks/models/" + fireworksModels["llama-3.1-70b"]

	request := FireworksRequest{
		Model: model,
		Messages: []FireworksMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   4000, // 增加token限制以支持更长的响应
		Temperature: 0.7,
		Stream:      false,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("请求序列化失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.Endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("API返回错误状态码 %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	var response FireworksResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("响应解析失败: %v", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("响应中没有选择项")
	}

	return response.Choices[0].Message.Content, nil
}

// callLLM 调用LLM API
func (c *DashScopeLLMClient) callLLM(prompt string) (string, error) {
	request := LLMRequest{
		Model: "qwen-turbo",
	}
	request.Input.Messages = []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}
	request.Parameters.Temperature = 0.7
	request.Parameters.MaxTokens = 2000

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("请求序列化失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.Endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	var response LLMResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("响应解析失败: %v", err)
	}

	return response.Output.Text, nil
}

// buildAggregationPrompt 构建聚合提示词
func (c *DashScopeLLMClient) buildAggregationPrompt(items []core.HotItem) string {
	var itemsText strings.Builder
	itemsText.WriteString("以下是从各大平台收集的热榜项目：\n\n")

	for i, item := range items {
		if i >= 50 { // 限制输入长度
			break
		}
		itemsText.WriteString(fmt.Sprintf("%d. [%s] %s (热度: %.0f)\n",
			i+1, item.Platform, item.Title, item.HotValue))
		if item.Description != "" {
			itemsText.WriteString(fmt.Sprintf("   描述: %s\n", item.Description))
		}
		itemsText.WriteString("\n")
	}

	prompt := fmt.Sprintf(`%s

请分析这些热榜项目，将相似的话题聚合在一起，生成5-8个高质量的话题。每个话题应该包含：
1. 话题标题（简洁明了）
2. 话题描述（详细说明）
3. 热度值（综合相关项目的热度）
4. 分类标签（如：科技、社会、娱乐、经济等）

请按以下JSON格式输出：
[
  {
    "title": "话题标题",
    "description": "话题详细描述",
    "hot_value": 热度数值,
    "categories": ["分类1", "分类2"]
  }
]

注意：
- 只输出JSON格式，不要其他文字
- 确保话题具有新闻价值和时效性
- 避免重复和过于相似的话题`, itemsText.String())

	return prompt
}

// buildArticlePrompt 构建文章生成提示词
func (c *DashScopeLLMClient) buildArticlePrompt(topic core.Topic, relatedNews []string) string {
	var newsText strings.Builder
	if len(relatedNews) > 0 {
		newsText.WriteString("相关新闻链接：\n")
		for i, url := range relatedNews {
			if i >= 5 { // 限制相关新闻数量
				break
			}
			newsText.WriteString(fmt.Sprintf("- %s\n", url))
		}
		newsText.WriteString("\n")
	}

	prompt := fmt.Sprintf(`请基于以下话题生成一篇高质量的新闻文章：

话题标题：%s
话题描述：%s
话题分类：%s

%s

请生成一篇800-1200字的新闻文章，要求：
1. 标题吸引人且准确
2. 内容结构清晰（引言、正文、结论）
3. 语言专业且易懂
4. 包含客观分析和见解
5. 适合发布到印象笔记

请按以下JSON格式输出：
{
  "title": "文章标题",
  "content": "文章正文内容",
  "summary": "文章摘要（100-150字）"
}

注意：只输出JSON格式，不要其他文字。`,
		topic.Title,
		topic.Description,
		strings.Join(topic.Categories, ", "),
		newsText.String())

	return prompt
}

// parseTopicsFromResponse 从响应中解析话题
func (c *DashScopeLLMClient) parseTopicsFromResponse(response string) ([]core.Topic, error) {
	// 清理响应文本
	response = strings.TrimSpace(response)

	// 尝试提取JSON部分
	start := strings.Index(response, "[")
	end := strings.LastIndex(response, "]")
	if start == -1 || end == -1 {
		return nil, fmt.Errorf("响应中未找到有效的JSON数组")
	}

	jsonStr := response[start : end+1]

	var rawTopics []struct {
		Title       string   `json:"title"`
		Description string   `json:"description"`
		HotValue    float64  `json:"hot_value"`
		Categories  []string `json:"categories"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &rawTopics); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	var topics []core.Topic
	for i, raw := range rawTopics {
		topic := core.Topic{
			ID:          fmt.Sprintf("topic_%d_%d", time.Now().Unix(), i),
			Title:       raw.Title,
			Description: raw.Description,
			HotValue:    raw.HotValue,
			Categories:  raw.Categories,
			Platform:    "聚合",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		topics = append(topics, topic)
	}

	return topics, nil
}

// parseArticleFromResponse 从响应中解析文章
func (c *DashScopeLLMClient) parseArticleFromResponse(response string, topic core.Topic) (*core.Article, error) {
	// 清理响应文本
	response = strings.TrimSpace(response)

	// 尝试提取JSON部分
	start := strings.Index(response, "{")
	end := strings.LastIndex(response, "}")
	if start == -1 || end == -1 {
		return nil, fmt.Errorf("响应中未找到有效的JSON对象")
	}

	jsonStr := response[start : end+1]

	var rawArticle struct {
		Title   string `json:"title"`
		Content string `json:"content"`
		Summary string `json:"summary"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &rawArticle); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	article := &core.Article{
		Id:        fmt.Sprintf("article_%d", time.Now().Unix()),
		Title:     rawArticle.Title,
		Content:   rawArticle.Content,
		Summary:   rawArticle.Summary,
		TopicId:   topic.ID,
		WordCount: len([]rune(rawArticle.Content)),
		CreatedAt: time.Now(),
		Status:    "draft",
	}

	return article, nil
}

// Fireworks AI 客户端的辅助方法

// buildAggregationPrompt 构建聚合提示词 (Fireworks版本)
func (c *FireworksLLMClient) buildAggregationPrompt(items []core.HotItem) string {
	var itemsText strings.Builder
	itemsText.WriteString("以下是从各大平台收集的热榜项目：\n\n")

	for i, item := range items {
		if i >= 50 { // 限制输入长度
			break
		}
		itemsText.WriteString(fmt.Sprintf("%d. [%s] %s (热度: %.0f)\n",
			i+1, item.Platform, item.Title, item.HotValue))
		if item.Description != "" {
			itemsText.WriteString(fmt.Sprintf("   描述: %s\n", item.Description))
		}
		itemsText.WriteString("\n")
	}

	prompt := fmt.Sprintf(`%s

请分析这些热榜项目，将相似的话题聚合在一起，生成5-8个高质量的话题。每个话题应该包含：
1. 话题标题（简洁明了）
2. 话题描述（详细说明）
3. 热度值（综合相关项目的热度）
4. 分类标签（如：科技、社会、娱乐、经济等）

请按以下JSON格式输出：
[
  {
    "title": "话题标题",
    "description": "话题详细描述",
    "hot_value": 热度数值,
    "categories": ["分类1", "分类2"]
  }
]

注意：
- 只输出JSON格式，不要其他文字
- 确保话题具有新闻价值和时效性
- 避免重复和过于相似的话题`, itemsText.String())

	return prompt
}

// buildArticlePrompt 构建文章生成提示词 (Fireworks版本)
func (c *FireworksLLMClient) buildArticlePrompt(topic core.Topic, relatedNews []string) string {
	var newsText strings.Builder
	if len(relatedNews) > 0 {
		newsText.WriteString("相关新闻链接：\n")
		for i, url := range relatedNews {
			if i >= 5 { // 限制相关新闻数量
				break
			}
			newsText.WriteString(fmt.Sprintf("- %s\n", url))
		}
		newsText.WriteString("\n")
	}

	prompt := fmt.Sprintf(`请基于以下话题生成一篇高质量的新闻文章：

话题标题：%s
话题描述：%s
话题分类：%s

%s

请生成一篇800-1200字的新闻文章，要求：
1. 标题吸引人且准确
2. 内容结构清晰（引言、正文、结论）
3. 语言专业且易懂
4. 包含客观分析和见解
5. 适合发布到印象笔记

请按以下JSON格式输出：
{
  "title": "文章标题",
  "content": "文章正文内容",
  "summary": "文章摘要（100-150字）"
}

注意：只输出JSON格式，不要其他文字。`,
		topic.Title,
		topic.Description,
		strings.Join(topic.Categories, ", "),
		newsText.String())

	return prompt
}

// parseTopicsFromResponse 从响应中解析话题 (Fireworks版本)
func (c *FireworksLLMClient) parseTopicsFromResponse(response string) ([]core.Topic, error) {
	// 清理响应文本
	response = strings.TrimSpace(response)

	// 尝试提取JSON部分
	start := strings.Index(response, "[")
	end := strings.LastIndex(response, "]")
	if start == -1 || end == -1 {
		return nil, fmt.Errorf("响应中未找到有效的JSON数组")
	}

	jsonStr := response[start : end+1]

	var rawTopics []struct {
		Title       string   `json:"title"`
		Description string   `json:"description"`
		HotValue    float64  `json:"hot_value"`
		Categories  []string `json:"categories"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &rawTopics); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	var topics []core.Topic
	for i, raw := range rawTopics {
		topic := core.Topic{
			ID:          fmt.Sprintf("fireworks_topic_%d_%d", time.Now().Unix(), i),
			Title:       raw.Title,
			Description: raw.Description,
			HotValue:    raw.HotValue,
			Categories:  raw.Categories,
			Platform:    "Fireworks聚合",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		topics = append(topics, topic)
	}

	return topics, nil
}

// parseArticleFromResponse 从响应中解析文章 (Fireworks版本)
func (c *FireworksLLMClient) parseArticleFromResponse(response string, topic core.Topic) (*core.Article, error) {
	// 清理响应文本
	response = strings.TrimSpace(response)

	// 尝试提取JSON部分
	start := strings.Index(response, "{")
	end := strings.LastIndex(response, "}")
	if start == -1 || end == -1 {
		return nil, fmt.Errorf("响应中未找到有效的JSON对象")
	}

	jsonStr := response[start : end+1]

	var rawArticle struct {
		Title   string `json:"title"`
		Content string `json:"content"`
		Summary string `json:"summary"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &rawArticle); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	article := &core.Article{
		Id:        fmt.Sprintf("fireworks_article_%d", time.Now().Unix()),
		Title:     rawArticle.Title,
		Content:   rawArticle.Content,
		Summary:   rawArticle.Summary,
		TopicId:   topic.ID,
		WordCount: len([]rune(rawArticle.Content)),
		CreatedAt: time.Now(),
		Status:    "draft",
	}

	return article, nil
}
