package models

// NewsItem 新闻项数据结构
type NewsItem struct {
	Source      string `json:"source"`       // 来源
	Title       string `json:"title"`        // 标题
	URL         string `json:"url"`          // 链接
	Timestamp   int64  `json:"timestamp"`    // 时间戳
	Content     string `json:"content"`      // 完整新闻内容
	Summary     string `json:"summary"`      // 内容摘要
	Author      string `json:"author"`       // 作者
	PublishTime string `json:"publish_time"` // 发布时间
	WordCount   int    `json:"word_count"`   // 字数统计
}
