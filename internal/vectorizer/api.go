package vectorizer

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"newsBot/internal/config"
)

// VectorizeRequest 向量化请求结构
type VectorizeRequest struct {
	Model string `json:"model"`
	Input struct {
		Texts []string `json:"texts"`
	} `json:"input"`
}

// VectorizeResponse 向量化响应结构
type VectorizeResponse struct {
	Output struct {
		Embeddings []struct {
			Embedding []float32 `json:"embedding"`
			TextIndex int       `json:"text_index"`
		} `json:"embeddings"`
	} `json:"output"`
	Usage struct {
		TotalTokens int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// BatchVectorizeRequest 批量向量化请求结构
type BatchVectorizeRequest struct {
	Model string `json:"model"`
	Input struct {
		Texts []string `json:"texts"`
	} `json:"input"`
}

// BatchVectorizeResponse 批量向量化响应结构
type BatchVectorizeResponse struct {
	Output struct {
		Embeddings [][]float32 `json:"embeddings"`
	} `json:"output"`
	Usage struct {
		TotalTokens int `json:"total_tokens"`
	} `json:"usage"`
	RequestID string `json:"request_id"`
}

// VectorizeText 单个文本向量化
func VectorizeText(text string) ([]float32, error) {
	if text == "" {
		return nil, fmt.Errorf("文本内容为空")
	}

	// 清理和预处理文本
	text = preprocessText(text)

	// 从配置文件获取API密钥和端点
	cfg := config.GetConfig()
	if cfg == nil {
		return nil, fmt.Errorf("配置加载失败")
	}

	apiKey := cfg.Dashscope.ApiKey
	if apiKey == "" {
		return nil, fmt.Errorf("Dashscope API密钥未配置")
	}

	endpoint := cfg.Dashscope.EmbeddingEndpoint
	if endpoint == "" {
		endpoint = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
	}

	// 构建请求
	request := VectorizeRequest{
		Model: "text-embedding-v1",
	}
	request.Input.Texts = []string{text}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("请求序列化失败: %v", err)
	}

	// 创建HTTP客户端
	client := resty.New()
	client.SetTimeout(30 * time.Second)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(1 * time.Second)

	// 发送请求
	resp, err := client.R().
		SetHeader("Authorization", "Bearer "+apiKey).
		SetHeader("Content-Type", "application/json").
		SetBody(requestBody).
		Post(endpoint)

	if err != nil {
		return nil, fmt.Errorf("API请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	// 解析响应
	var result VectorizeResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("响应解析失败: %v", err)
	}

	if len(result.Output.Embeddings) == 0 || len(result.Output.Embeddings[0].Embedding) == 0 {
		return nil, fmt.Errorf("返回的向量为空")
	}

	log.Printf("文本向量化成功，向量维度: %d, 使用token: %d",
		len(result.Output.Embeddings[0].Embedding), result.Usage.TotalTokens)

	return result.Output.Embeddings[0].Embedding, nil
}

// BatchVectorize 批量文本向量化（最多25条/批次）
func BatchVectorize(texts []string) ([][]float32, error) {
	if len(texts) == 0 {
		return nil, fmt.Errorf("文本列表为空")
	}

	if len(texts) > 25 {
		return nil, fmt.Errorf("批量处理最多支持25条文本，当前: %d", len(texts))
	}

	// 预处理所有文本
	processedTexts := make([]string, len(texts))
	for i, text := range texts {
		processedTexts[i] = preprocessText(text)
	}

	// 从配置文件获取API密钥和端点
	cfg := config.GetConfig()
	if cfg == nil {
		return nil, fmt.Errorf("配置加载失败")
	}

	apiKey := cfg.Dashscope.ApiKey
	if apiKey == "" {
		return nil, fmt.Errorf("Dashscope API密钥未配置")
	}

	endpoint := cfg.Dashscope.EmbeddingEndpoint
	if endpoint == "" {
		endpoint = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
	}

	// 构建批量请求
	request := BatchVectorizeRequest{
		Model: "text-embedding-v1",
	}
	request.Input.Texts = processedTexts

	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("批量请求序列化失败: %v", err)
	}

	// 创建HTTP客户端
	client := resty.New()
	client.SetTimeout(60 * time.Second) // 批量处理需要更长时间
	client.SetRetryCount(3)
	client.SetRetryWaitTime(2 * time.Second)

	// 发送请求
	resp, err := client.R().
		SetHeader("Authorization", "Bearer "+apiKey).
		SetHeader("Content-Type", "application/json").
		SetBody(requestBody).
		Post(endpoint)

	if err != nil {
		return nil, fmt.Errorf("批量API请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("批量API返回错误状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	// 解析响应
	var result BatchVectorizeResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("批量响应解析失败: %v", err)
	}

	if len(result.Output.Embeddings) != len(texts) {
		return nil, fmt.Errorf("返回的向量数量不匹配，期望: %d, 实际: %d",
			len(texts), len(result.Output.Embeddings))
	}

	log.Printf("批量文本向量化成功，处理文本数: %d, 使用token: %d",
		len(texts), result.Usage.TotalTokens)

	return result.Output.Embeddings, nil
}

// preprocessText 文本预处理
func preprocessText(text string) string {
	// 移除多余的空白字符
	text = strings.TrimSpace(text)

	// 替换多个连续空格为单个空格
	text = strings.ReplaceAll(text, "\n", " ")
	text = strings.ReplaceAll(text, "\t", " ")

	// 移除多余的空格
	for strings.Contains(text, "  ") {
		text = strings.ReplaceAll(text, "  ", " ")
	}

	// 限制文本长度（通义千问有token限制）
	if len(text) > 2000 {
		text = text[:2000]
	}

	return text
}

// GetVectorDimension 获取向量维度
func GetVectorDimension() int {
	// 通义千问 text-embedding-v1 模型的向量维度
	return 1536
}
