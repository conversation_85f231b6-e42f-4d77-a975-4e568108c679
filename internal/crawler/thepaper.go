package crawler

import (
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"log"
	"newsBot/internal/models"
	"newsBot/internal/utils"
	"regexp"
	"strings"
	"sync"
	"time"
)

// CrawlThePaper 爬取澎湃新闻
func CrawlThePaper() []models.NewsItem {
	var results []models.NewsItem
	client := utils.CreateClient()

	log.Println("开始爬取澎湃新闻...")

	// 方法1: 爬取澎湃新闻分页内容
	results = append(results, crawlThePaperPages(client)...)

	// 方法2: 爬取澎湃新闻首页
	results = append(results, crawlThePaperHomepage(client)...)

	log.Printf("澎湃新闻爬取完成，共获取 %d 条新闻", len(results))
	return results
}

// crawlThePaperPages 爬取澎湃新闻分页内容
func crawlThePaperPages(client *resty.Client) []models.NewsItem {
	var results []models.NewsItem
	var mu sync.Mutex

	page := 1
	maxPages := 3 // 限制爬取页数
	re := regexp.MustCompile(`data-id="(\d+)"`)

	for page <= maxPages {
		log.Printf("正在爬取澎湃新闻第 %d 页...", page)

		// 构建分页URL
		pageURL := fmt.Sprintf("https://www.thepaper.cn/load_index.jsp?nodeids=25462&pageidx=%d", page)

		resp, err := client.R().Get(pageURL)
		if err != nil {
			log.Printf("澎湃新闻第 %d 页请求失败: %v", page, err)
			break
		}

		content := resp.String()

		// 检查是否到达最后一页
		if strings.Contains(content, "没有找到相关内容") ||
			strings.Contains(content, "暂无数据") {
			log.Printf("澎湃新闻第 %d 页无更多内容", page)
			break
		}

		// 提取新闻ID
		matches := re.FindAllStringSubmatch(content, -1)
		if len(matches) == 0 {
			log.Printf("澎湃新闻第 %d 页未找到新闻ID", page)
			break
		}

		// 并发获取新闻详情
		var wg sync.WaitGroup
		semaphore := make(chan struct{}, 5) // 限制并发数

		for _, match := range matches {
			if len(match) < 2 {
				continue
			}

			newsID := match[1]

			wg.Add(1)
			go func(id string) {
				defer wg.Done()

				semaphore <- struct{}{}        // 获取信号量
				defer func() { <-semaphore }() // 释放信号量

				if newsItem := fetchThePaperNewsDetail(client, id); newsItem != nil {
					mu.Lock()
					results = append(results, *newsItem)
					mu.Unlock()
				}
			}(newsID)
		}

		wg.Wait()
		page++

		// 请求间隔
		time.Sleep(3 * time.Second)
	}

	return results
}

// fetchThePaperNewsDetail 获取澎湃新闻详情
func fetchThePaperNewsDetail(client *resty.Client, newsID string) *models.NewsItem {
	detailURL := fmt.Sprintf("https://www.thepaper.cn/newsDetail_forward_%s", newsID)

	resp, err := client.R().Get(detailURL)
	if err != nil {
		log.Printf("澎湃新闻详情 %s 请求失败: %v", newsID, err)
		return nil
	}

	content := resp.String()

	// 提取标题
	titleRe := regexp.MustCompile(`<h1[^>]*class="news_title"[^>]*>([^<]+)</h1>`)
	titleMatches := titleRe.FindStringSubmatch(content)
	if len(titleMatches) < 2 {
		// 尝试其他标题模式
		titleRe = regexp.MustCompile(`<title>([^<]+)</title>`)
		titleMatches = titleRe.FindStringSubmatch(content)
		if len(titleMatches) < 2 {
			return nil
		}
	}

	title := strings.TrimSpace(titleMatches[1])
	title = strings.ReplaceAll(title, "_澎湃新闻", "")
	title = strings.ReplaceAll(title, "-The Paper", "")

	// 提取内容摘要
	contentRe := regexp.MustCompile(`<div[^>]*class="news_txt"[^>]*>([^<]+)</div>`)
	contentMatches := contentRe.FindStringSubmatch(content)
	summary := ""
	if len(contentMatches) >= 2 {
		summary = strings.TrimSpace(contentMatches[1])
		if len(summary) > 200 {
			summary = summary[:200] + "..."
		}
	}

	return &models.NewsItem{
		Source:    "澎湃新闻",
		Title:     title,
		URL:       detailURL,
		Timestamp: time.Now().Unix(),
		Content:   summary,
	}
}

// crawlThePaperHomepage 爬取澎湃新闻首页
func crawlThePaperHomepage(client *resty.Client) []models.NewsItem {
	var results []models.NewsItem

	resp, err := client.R().Get("https://www.thepaper.cn/")
	if err != nil {
		log.Printf("澎湃新闻首页请求失败: %v", err)
		return results
	}

	content := resp.String()

	// 提取首页新闻链接
	linkRe := regexp.MustCompile(`<a[^>]+href="(/newsDetail_forward_\d+)"[^>]*>([^<]+)</a>`)
	matches := linkRe.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) < 3 {
			continue
		}

		link := "https://www.thepaper.cn" + match[1]
		title := strings.TrimSpace(match[2])

		if title != "" && len(title) > 5 {
			newsItem := models.NewsItem{
				Source:    "澎湃新闻",
				Title:     title,
				URL:       link,
				Timestamp: time.Now().Unix(),
				Content:   "",
			}
			results = append(results, newsItem)
		}
	}

	// 限制结果数量
	if len(results) > 20 {
		results = results[:20]
	}

	return results
}

// crawlThePaperAPI 尝试使用澎湃新闻API
func crawlThePaperAPI(client *resty.Client) []models.NewsItem {
	var results []models.NewsItem

	// 澎湃新闻可能的API端点
	apiURL := "https://www.thepaper.cn/api/list_25462_1.json"

	resp, err := client.R().Get(apiURL)
	if err != nil {
		log.Printf("澎湃新闻API请求失败: %v", err)
		return results
	}

	var apiResult struct {
		Data []struct {
			Title   string `json:"title"`
			URL     string `json:"url"`
			Summary string `json:"summary"`
			Time    string `json:"time"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.Body(), &apiResult); err != nil {
		log.Printf("澎湃新闻API响应解析失败: %v", err)
		return results
	}

	for _, item := range apiResult.Data {
		if item.Title != "" && item.URL != "" {
			newsItem := models.NewsItem{
				Source:    "澎湃新闻",
				Title:     item.Title,
				URL:       item.URL,
				Timestamp: time.Now().Unix(),
				Content:   item.Summary,
			}
			results = append(results, newsItem)
		}
	}

	return results
}
