package crawler

import (
	"log"
	"newsBot/internal/models"
	"newsBot/internal/utils"
	"sync"
	"time"
)

// ExtractFullContentForNews 为新闻列表提取完整内容
func ExtractFullContentForNews(news []models.NewsItem) []models.NewsItem {
	if len(news) == 0 {
		return news
	}

	log.Printf("开始提取 %d 条新闻的完整内容...", len(news))

	var wg sync.WaitGroup
	var mu sync.Mutex
	client := utils.CreateClient()

	// 限制并发数避免被封
	semaphore := make(chan struct{}, 2)

	for i := range news {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 提取完整内容
			fullContent := utils.ExtractFullContentFromURL(client, news[index].URL)

			// 生成摘要（取前200字符）
			summary := news[index].Content // 保留原有的简短摘要
			if summary == "" && len(fullContent) > 0 {
				if len(fullContent) > 200 {
					summary = fullContent[:200] + "..."
				} else {
					summary = fullContent
				}
			}

			// 计算字数
			wordCount := len([]rune(fullContent))

			// 更新新闻项
			mu.Lock()
			news[index].Content = fullContent
			news[index].Summary = summary
			news[index].WordCount = wordCount

			// 尝试提取发布时间和作者（简单实现）
			if news[index].PublishTime == "" {
				// 使用当前时间作为发布时间
				news[index].PublishTime = time.Now().Format("2006-01-02 15:04:05")
			}
			mu.Unlock()

			if len(fullContent) > 100 {
				log.Printf("已提取完整内容 [%s] %s (%d字)", news[index].Source, news[index].Title, wordCount)
			} else {
				log.Printf("内容提取不完整 [%s] %s (%d字)", news[index].Source, news[index].Title, wordCount)
			}

			// 添加延迟避免请求过快
			time.Sleep(time.Duration(2+index%3) * time.Second)
		}(i)
	}

	wg.Wait()

	// 统计提取结果
	fullContentCount := 0
	totalWords := 0
	for _, item := range news {
		if len(item.Content) > 100 {
			fullContentCount++
		}
		totalWords += item.WordCount
	}

	log.Printf("完整内容提取完成: %d/%d 成功, 总字数: %d", fullContentCount, len(news), totalWords)
	return news
}

// CrawlWithContentEnhancement 带内容增强的综合爬取
func CrawlWithContentEnhancement() []models.NewsItem {
	log.Println("开始带内容增强的新闻爬取...")

	var allNews []models.NewsItem

	// 并发爬取所有源
	var wg sync.WaitGroup
	results := make(chan []models.NewsItem, 4)

	crawlers := []func() []models.NewsItem{
		CrawlXinhua,
		CrawlPeople,
		CrawlCCTV,
		CrawlThePaper,
	}

	for _, crawler := range crawlers {
		wg.Add(1)
		go func(crawlFn func() []models.NewsItem) {
			defer wg.Done()
			results <- crawlFn()
		}(crawler)
	}

	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	for newsList := range results {
		allNews = append(allNews, newsList...)
	}

	// 增强内容（使用完整内容提取）
	allNews = ExtractFullContentForNews(allNews)

	log.Printf("带内容增强的爬取完成，共获取 %d 条新闻", len(allNews))
	return allNews
}
