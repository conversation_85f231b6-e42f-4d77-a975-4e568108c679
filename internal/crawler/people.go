package crawler

import (
	"context"
	"log"
	"newsBot/internal/models"
	"newsBot/internal/utils"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/chromedp/chromedp"
)

// CrawlPeople 爬取人民网新闻（直接HTTP请求）
func CrawlPeople() []models.NewsItem {
	var results []models.NewsItem

	log.Println("开始爬取人民网...")

	// 直接使用HTTP请求，避免Chrome依赖
	client := utils.CreateClient()

	// 尝试多个人民网页面
	urls := []string{
		"http://www.people.com.cn/",
		"http://politics.people.com.cn/",
		"http://society.people.com.cn/",
		"http://world.people.com.cn/",
	}

	for _, url := range urls {
		resp, err := client.R().Get(url)
		if err != nil {
			log.Printf("人民网页面 %s 请求失败: %v", url, err)
			continue
		}

		doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
		if err != nil {
			log.Printf("人民网页面 %s 解析失败: %v", url, err)
			continue
		}

		// 查找新闻链接 - 使用更精确的选择器
		selectors := []string{
			".news_list a",
			".list a",
			"h3 a",
			"h4 a",
			".title a",
			"li a",
		}

		for _, selector := range selectors {
			doc.Find(selector).Each(func(i int, s *goquery.Selection) {
				title := strings.TrimSpace(s.Text())
				link, exists := s.Attr("href")

				if !exists || title == "" || len(title) < 15 {
					return
				}

				// 过滤有效的新闻链接
				if (strings.Contains(link, "people.com.cn") || strings.HasPrefix(link, "/")) &&
					!strings.Contains(link, "javascript") &&
					!strings.Contains(link, "#") &&
					len(title) > 10 {

					// 确保链接是完整的URL
					if strings.HasPrefix(link, "/") {
						link = "http://www.people.com.cn" + link
					}

					newsItem := models.NewsItem{
						Source:    "人民网",
						Title:     title,
						URL:       link,
						Timestamp: time.Now().Unix(),
						Content:   "", // 稍后提取完整内容
					}
					results = append(results, newsItem)

					// 限制每个选择器的结果数量
					if len(results) >= 8 {
						return
					}
				}
			})

			if len(results) >= 8 {
				break
			}
		}

		if len(results) >= 8 {
			break
		}
	}

	// 去重
	seen := make(map[string]bool)
	var uniqueResults []models.NewsItem
	for _, item := range results {
		if !seen[item.URL] {
			seen[item.URL] = true
			uniqueResults = append(uniqueResults, item)
		}
	}

	// 限制结果数量
	if len(uniqueResults) > 6 {
		uniqueResults = uniqueResults[:6]
	}

	log.Printf("人民网爬取完成，共获取 %d 条新闻", len(uniqueResults))
	return uniqueResults
}

// crawlPeopleHomepage 备用方案：直接爬取人民网首页
func crawlPeopleHomepage() []models.NewsItem {
	var results []models.NewsItem

	log.Println("使用备用方案爬取人民网首页...")

	// 创建简单的上下文用于访问首页
	ctx, cancel := chromedp.NewContext(context.Background())
	defer cancel()

	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	var htmlContent string

	err := chromedp.Run(ctx,
		chromedp.Navigate("http://www.people.com.cn/"),
		chromedp.Sleep(3*time.Second),
		chromedp.OuterHTML("html", &htmlContent),
	)

	if err != nil {
		log.Printf("人民网首页访问失败: %v", err)
		return results
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		log.Printf("人民网首页解析失败: %v", err)
		return results
	}

	// 查找首页新闻链接
	doc.Find("a").Each(func(i int, s *goquery.Selection) {
		title := strings.TrimSpace(s.Text())
		link, exists := s.Attr("href")

		if !exists || title == "" || len(title) < 10 {
			return
		}

		// 过滤有效的新闻链接
		if strings.Contains(link, "people.com.cn") &&
			(strings.Contains(link, "/politics/") ||
				strings.Contains(link, "/society/") ||
				strings.Contains(link, "/world/") ||
				strings.Contains(strings.ToLower(title), "热点") ||
				strings.Contains(strings.ToLower(title), "重要")) {

			// 确保链接是完整的URL
			if strings.HasPrefix(link, "/") {
				link = "http://www.people.com.cn" + link
			}

			// 尝试提取内容摘要
			content := utils.ExtractSummaryFromTitle(title)

			newsItem := models.NewsItem{
				Source:    "人民网",
				Title:     title,
				URL:       link,
				Timestamp: time.Now().Unix(),
				Content:   content,
			}
			results = append(results, newsItem)
		}
	})

	// 限制结果数量
	if len(results) > 15 {
		results = results[:15]
	}

	return results
}
