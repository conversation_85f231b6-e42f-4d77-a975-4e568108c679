package crawler

import (
	"encoding/json"
	"log"
	"newsBot/internal/models"
	"newsBot/internal/utils"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-resty/resty/v2"
)

// CrawlXinhua 爬取新华网新闻
func CrawlXinhua() []models.NewsItem {
	var results []models.NewsItem
	client := utils.CreateClient()

	log.Println("开始爬取新华网...")

	// 方法1: 爬取首页热点新闻
	results = append(results, crawlXinhuaHomepage(client)...)

	// 方法2: 使用API搜索热点新闻
	results = append(results, crawlXinhuaAPI(client)...)

	log.Printf("新华网爬取完成，共获取 %d 条新闻", len(results))
	return results
}

// crawlXinhuaHomepage 爬取新华网首页热点
func crawlXinhuaHomepage(client *resty.Client) []models.NewsItem {
	var results []models.NewsItem

	// 尝试多个新华网新闻页面
	urls := []string{
		"http://www.xinhuanet.com/politics/",
		"http://www.xinhuanet.com/local/",
		"http://www.xinhuanet.com/world/",
		"http://www.xinhuanet.com/",
	}

	for _, url := range urls {
		resp, err := client.R().Get(url)
		if err != nil {
			log.Printf("新华网页面 %s 请求失败: %v", url, err)
			continue
		}

		doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
		if err != nil {
			log.Printf("新华网页面 %s 解析失败: %v", url, err)
			continue
		}

		// 查找新闻链接 - 使用更精确的选择器
		selectors := []string{
			".focusNews a",
			".topNews a",
			".news-list a",
			".titleList a",
			".linkList a",
			"h3 a",
			"h4 a",
			"h2 a",
			".title a",
			"li a",
			"[href*='/politics/']",
			"[href*='/local/']",
			"[href*='/world/']",
		}

		for _, selector := range selectors {
			doc.Find(selector).Each(func(i int, s *goquery.Selection) {
				title := strings.TrimSpace(s.Text())
				link, exists := s.Attr("href")

				if !exists || title == "" || len(title) < 15 {
					return
				}

				// 过滤有效的新闻链接
				if (strings.Contains(link, "xinhuanet.com") || strings.HasPrefix(link, "/")) &&
					(strings.Contains(link, "/politics/") ||
						strings.Contains(link, "/local/") ||
						strings.Contains(link, "/world/") ||
						strings.Contains(link, "/fortune/") ||
						strings.Contains(link, "/tech/") ||
						strings.Contains(link, "/society/") ||
						strings.Contains(link, "/comments/") ||
						(strings.Contains(link, ".html") && len(title) > 15)) {

					// 确保链接是完整的URL
					if strings.HasPrefix(link, "/") {
						link = "http://www.xinhuanet.com" + link
					}

					newsItem := models.NewsItem{
						Source:    "新华网",
						Title:     title,
						URL:       link,
						Timestamp: time.Now().Unix(),
						Content:   "", // 稍后提取完整内容
					}
					results = append(results, newsItem)

					// 限制每个选择器的结果数量
					if len(results) >= 10 {
						return
					}
				}
			})

			if len(results) >= 10 {
				break
			}
		}

		if len(results) >= 10 {
			break
		}
	}

	// 去重
	seen := make(map[string]bool)
	var uniqueResults []models.NewsItem
	for _, item := range results {
		if !seen[item.URL] {
			seen[item.URL] = true
			uniqueResults = append(uniqueResults, item)
		}
	}

	// 限制结果数量
	if len(uniqueResults) > 8 {
		uniqueResults = uniqueResults[:8]
	}

	return uniqueResults
}

// crawlXinhuaAPI 使用新华网API搜索
func crawlXinhuaAPI(client *resty.Client) []models.NewsItem {
	var results []models.NewsItem

	// 尝试多个新华网API端点
	apiURLs := []string{
		"http://qc.wa.news.cn/nodeart/list?nid=11147664&pgnum=1&cnt=10&tp=1&orderby=1",
		"http://qc.wa.news.cn/nodeart/list?nid=11147664&pgnum=1&cnt=20",
	}

	for _, apiURL := range apiURLs {
		resp, err := client.R().Get(apiURL)
		if err != nil {
			log.Printf("新华网API %s 请求失败: %v", apiURL, err)
			continue
		}

		// 尝试解析不同的API响应格式
		var apiResult1 struct {
			Data struct {
				List []struct {
					Title   string `json:"title"`
					URL     string `json:"url"`
					PubTime string `json:"pubtime"`
					Desc    string `json:"desc"`
				} `json:"list"`
			} `json:"data"`
		}

		if err := json.Unmarshal(resp.Body(), &apiResult1); err == nil && len(apiResult1.Data.List) > 0 {
			// 处理API结果
			for _, item := range apiResult1.Data.List {
				if item.Title != "" && item.URL != "" {
					newsItem := models.NewsItem{
						Source:    "新华网",
						Title:     item.Title,
						URL:       item.URL,
						Timestamp: time.Now().Unix(),
						Content:   item.Desc,
					}
					results = append(results, newsItem)
				}
			}
			break // 成功获取数据就退出
		}

		// 尝试另一种格式
		var apiResult2 struct {
			Content struct {
				Results []struct {
					Title   string `json:"title"`
					URL     string `json:"url"`
					PubTime string `json:"pubtime"`
					Desc    string `json:"desc"`
				} `json:"results"`
			} `json:"content"`
		}

		if err := json.Unmarshal(resp.Body(), &apiResult2); err == nil && len(apiResult2.Content.Results) > 0 {
			// 处理API结果
			for _, item := range apiResult2.Content.Results {
				if item.Title != "" && item.URL != "" {
					newsItem := models.NewsItem{
						Source:    "新华网",
						Title:     item.Title,
						URL:       item.URL,
						Timestamp: time.Now().Unix(),
						Content:   item.Desc,
					}
					results = append(results, newsItem)
				}
			}
			break // 成功获取数据就退出
		}

		log.Printf("新华网API %s 响应格式不匹配", apiURL)
	}

	// 限制结果数量
	if len(results) > 8 {
		results = results[:8]
	}

	return results
}
