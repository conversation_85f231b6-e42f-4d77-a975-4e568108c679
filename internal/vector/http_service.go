package vector

import (
	"fmt"
	"log"
	"newsBot/internal/models"
	"strings"
	"sync"
	"time"

	"newsBot/internal/qdrant"
	"newsBot/internal/vectorizer"
)

// HTTPService 基于HTTP的向量化服务
type HTTPService struct {
	qdrantClient *qdrant.HTTPClient
	mu           sync.RWMutex
}

// NewHTTPService 创建新的HTTP向量化服务
func NewHTTPService() (*HTTPService, error) {
	// 创建Qdrant HTTP客户端
	client, err := qdrant.NewHTTPClient()
	if err != nil {
		return nil, fmt.Errorf("创建Qdrant HTTP客户端失败: %v", err)
	}

	// 初始化集合
	if err := client.InitializeCollection(); err != nil {
		return nil, fmt.Errorf("初始化集合失败: %v", err)
	}

	service := &HTTPService{
		qdrantClient: client,
	}

	log.Println("HTTP向量化服务初始化成功")
	return service, nil
}

// ProcessNews 处理单条新闻（向量化并存储）
func (s *HTTPService) ProcessNews(news models.NewsItem) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 构建用于向量化的文本
	text := s.buildTextForVectorization(news)

	// 向量化
	vector, err := vectorizer.VectorizeText(text)
	if err != nil {
		return fmt.Errorf("新闻向量化失败: %v", err)
	}

	// 存储到Qdrant
	if err := s.qdrantClient.StoreNews(news, vector); err != nil {
		return fmt.Errorf("存储新闻向量失败: %v", err)
	}

	log.Printf("新闻处理完成: %s", news.Title)
	return nil
}

// ProcessNewsBatch 批量处理新闻（向量化并存储）
func (s *HTTPService) ProcessNewsBatch(newsList []models.NewsItem) error {
	if len(newsList) == 0 {
		return fmt.Errorf("新闻列表为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 构建文本列表
	texts := make([]string, len(newsList))
	for i, news := range newsList {
		texts[i] = s.buildTextForVectorization(news)
	}

	// 分批处理（每批最多25条）
	batchSize := 25
	for i := 0; i < len(newsList); i += batchSize {
		end := i + batchSize
		if end > len(newsList) {
			end = len(newsList)
		}

		batchNews := newsList[i:end]
		batchTexts := texts[i:end]

		// 单个向量化处理（避免批量API问题）
		vectors := make([][]float32, len(batchTexts))
		for j, text := range batchTexts {
			vector, err := vectorizer.VectorizeText(text)
			if err != nil {
				log.Printf("向量化失败: %v", err)
				continue
			}
			vectors[j] = vector
		}

		// 批量存储
		for j, news := range batchNews {
			if len(vectors[j]) > 0 {
				if err := s.qdrantClient.StoreNews(news, vectors[j]); err != nil {
					log.Printf("存储新闻失败: %v", err)
				} else {
					log.Printf("新闻处理完成: %s", news.Title)
				}
			}
		}

		log.Printf("批量处理完成: %d 条新闻", len(batchNews))

		// 添加延迟避免API限制
		if end < len(newsList) {
			time.Sleep(100 * time.Millisecond)
		}
	}

	log.Printf("所有新闻批量处理完成，共 %d 条", len(newsList))
	return nil
}

// SearchByText 根据文本搜索相似新闻
func (s *HTTPService) SearchByText(text string, limit int) ([]models.NewsItem, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if strings.TrimSpace(text) == "" {
		return nil, fmt.Errorf("搜索文本不能为空")
	}

	// 向量化搜索文本
	vector, err := vectorizer.VectorizeText(text)
	if err != nil {
		return nil, fmt.Errorf("搜索文本向量化失败: %v", err)
	}

	// 搜索相似新闻
	return s.qdrantClient.SearchSimilar(vector, limit)
}

// SearchSimilarNews 搜索相似新闻
func (s *HTTPService) SearchSimilarNews(queryNews models.NewsItem, limit int) ([]models.NewsItem, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 构建查询文本
	text := s.buildTextForVectorization(queryNews)

	// 向量化查询文本
	vector, err := vectorizer.VectorizeText(text)
	if err != nil {
		return nil, fmt.Errorf("查询文本向量化失败: %v", err)
	}

	// 搜索相似新闻
	return s.qdrantClient.SearchSimilar(vector, limit)
}

// GetCollectionStats 获取集合统计信息
func (s *HTTPService) GetCollectionStats() (map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	collectionName := "news_vectors"
	return s.qdrantClient.GetCollectionStats(collectionName)
}

// GetVectorDimension 获取向量维度
func (s *HTTPService) GetVectorDimension() int {
	return vectorizer.GetVectorDimension()
}

// HealthCheck 健康检查
func (s *HTTPService) HealthCheck() error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 检查Qdrant连接
	_, err := s.GetCollectionStats()
	if err != nil {
		return fmt.Errorf("Qdrant健康检查失败: %v", err)
	}

	// 检查向量化API（使用简单文本测试）
	_, err = vectorizer.VectorizeText("健康检查测试")
	if err != nil {
		return fmt.Errorf("向量化API健康检查失败: %v", err)
	}

	return nil
}

// Close 关闭服务（HTTP客户端不需要特殊关闭）
func (s *HTTPService) Close() error {
	// HTTP客户端不需要特殊关闭操作
	return nil
}

// CleanOldNews 清理旧新闻（暂未实现）
func (s *HTTPService) CleanOldNews(daysToKeep int) error {
	// TODO: 实现清理旧新闻功能
	log.Printf("清理 %d 天前的旧新闻功能暂未实现", daysToKeep)
	return nil
}

// buildTextForVectorization 构建用于向量化的文本
func (s *HTTPService) buildTextForVectorization(news models.NewsItem) string {
	var parts []string

	// 标题（权重最高）
	if news.Title != "" {
		parts = append(parts, news.Title)
	}

	// 摘要
	if news.Summary != "" {
		parts = append(parts, news.Summary)
	}

	// 内容（截取前500字符）
	if news.Content != "" {
		content := news.Content
		if len(content) > 500 {
			content = content[:500]
		}
		parts = append(parts, content)
	}

	// 如果没有内容，至少使用标题
	if len(parts) == 0 && news.Title != "" {
		parts = append(parts, news.Title)
	}

	// 组合文本
	text := strings.Join(parts, ". ")

	// 添加来源信息
	if news.Source != "" {
		text = fmt.Sprintf("[%s] %s", news.Source, text)
	}

	return text
}
