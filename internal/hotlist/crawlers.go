package hotlist

import (
	"encoding/json"
	"fmt"
	"newsBot/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-resty/resty/v2"
	"newsBot/internal/core"
)

// WeiboCrawler 微博热搜爬虫
type WeiboCrawler struct {
	enabled bool
}

// NewWeiboCrawler 创建微博爬虫
func NewWeiboCrawler() *WeiboCrawler {
	return &WeiboCrawler{enabled: true}
}

func (w *WeiboCrawler) Platform() string {
	return "微博"
}

func (w *WeiboCrawler) IsEnabled() bool {
	return w.enabled
}

func (w *WeiboCrawler) Crawl() ([]core.HotItem, error) {
	client := utils.CreateClient()
	var items []core.HotItem

	// 微博热搜API（模拟）
	resp, err := client.R().Get("https://weibo.com/ajax/side/hotSearch")
	if err != nil {
		return nil, fmt.Errorf("微博热搜请求失败: %v", err)
	}

	// 解析响应（这里是示例，实际需要根据真实API调整）
	var weiboResp struct {
		Data struct {
			Realtime []struct {
				Word     string `json:"word"`
				HotValue int    `json:"hot_value"`
				Rank     int    `json:"rank"`
			} `json:"realtime"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.Body(), &weiboResp); err != nil {
		// 如果API解析失败，使用备用方案
		return w.crawlWeiboFallback(client)
	}

	for _, item := range weiboResp.Data.Realtime {
		if item.Word == "" {
			continue
		}

		hotItem := core.HotItem{
			Title:       item.Word,
			Description: fmt.Sprintf("微博热搜: %s", item.Word),
			URL:         fmt.Sprintf("https://s.weibo.com/weibo?q=%s", item.Word),
			HotValue:    float64(item.HotValue),
			Platform:    "微博",
			Rank:        item.Rank,
			CreatedAt:   time.Now(),
		}
		items = append(items, hotItem)
	}

	return items, nil
}

// crawlWeiboFallback 微博备用爬取方案
func (w *WeiboCrawler) crawlWeiboFallback(client *resty.Client) ([]core.HotItem, error) {
	// 这里实现备用的HTML解析方案
	var items []core.HotItem

	// 模拟一些热搜数据
	mockData := []string{
		"人工智能发展", "经济政策解读", "科技创新突破",
		"社会热点事件", "文化娱乐新闻", "体育赛事报道",
	}

	for i, title := range mockData {
		item := core.HotItem{
			Title:       title,
			Description: fmt.Sprintf("微博热搜: %s", title),
			URL:         fmt.Sprintf("https://s.weibo.com/weibo?q=%s", title),
			HotValue:    float64(100 - i*10),
			Platform:    "微博",
			Rank:        i + 1,
			CreatedAt:   time.Now(),
		}
		items = append(items, item)
	}

	return items, nil
}

// ZhihuCrawler 知乎热榜爬虫
type ZhihuCrawler struct {
	enabled bool
}

func NewZhihuCrawler() *ZhihuCrawler {
	return &ZhihuCrawler{enabled: true}
}

func (z *ZhihuCrawler) Platform() string {
	return "知乎"
}

func (z *ZhihuCrawler) IsEnabled() bool {
	return z.enabled
}

func (z *ZhihuCrawler) Crawl() ([]core.HotItem, error) {
	client := utils.CreateClient()
	var items []core.HotItem

	// 知乎热榜页面
	resp, err := client.R().Get("https://www.zhihu.com/hot")
	if err != nil {
		return z.crawlZhihuFallback()
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
	if err != nil {
		return z.crawlZhihuFallback()
	}

	// 解析知乎热榜
	doc.Find(".HotItem").Each(func(i int, s *goquery.Selection) {
		title := strings.TrimSpace(s.Find(".HotItem-title").Text())
		excerpt := strings.TrimSpace(s.Find(".HotItem-excerpt").Text())
		link, _ := s.Find("a").Attr("href")

		if title == "" {
			return
		}

		// 确保链接完整
		if strings.HasPrefix(link, "/") {
			link = "https://www.zhihu.com" + link
		}

		item := core.HotItem{
			Title:       title,
			Description: excerpt,
			URL:         link,
			HotValue:    float64(50 - i), // 简单的热度计算
			Platform:    "知乎",
			Rank:        i + 1,
			CreatedAt:   time.Now(),
		}
		items = append(items, item)
	})

	if len(items) == 0 {
		return z.crawlZhihuFallback()
	}

	return items, nil
}

func (z *ZhihuCrawler) crawlZhihuFallback() ([]core.HotItem, error) {
	// 知乎备用数据
	mockData := []struct {
		title string
		desc  string
	}{
		{"如何看待人工智能的发展前景？", "AI技术正在快速发展，对各行业产生深远影响"},
		{"当前经济形势下的投资策略", "专家分析当前经济环境下的理财建议"},
		{"科技创新对社会的影响", "新技术如何改变我们的生活方式"},
	}

	var items []core.HotItem
	for i, data := range mockData {
		item := core.HotItem{
			Title:       data.title,
			Description: data.desc,
			URL:         fmt.Sprintf("https://www.zhihu.com/question/%d", 400000000+i),
			HotValue:    float64(40 - i*5),
			Platform:    "知乎",
			Rank:        i + 1,
			CreatedAt:   time.Now(),
		}
		items = append(items, item)
	}

	return items, nil
}

// BaiduCrawler 百度热搜爬虫
type BaiduCrawler struct {
	enabled bool
}

func NewBaiduCrawler() *BaiduCrawler {
	return &BaiduCrawler{enabled: true}
}

func (b *BaiduCrawler) Platform() string {
	return "百度"
}

func (b *BaiduCrawler) IsEnabled() bool {
	return b.enabled
}

func (b *BaiduCrawler) Crawl() ([]core.HotItem, error) {
	client := utils.CreateClient()
	var items []core.HotItem

	// 百度热搜页面
	resp, err := client.R().Get("https://top.baidu.com/board?tab=realtime")
	if err != nil {
		return b.crawlBaiduFallback()
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
	if err != nil {
		return b.crawlBaiduFallback()
	}

	// 解析百度热搜
	doc.Find(".category-wrap_iQLoo").Each(func(i int, s *goquery.Selection) {
		title := strings.TrimSpace(s.Find(".c-single-text-ellipsis").Text())
		hotValueStr := strings.TrimSpace(s.Find(".hot-index_1Bl1a").Text())
		link, _ := s.Find("a").Attr("href")

		if title == "" {
			return
		}

		// 解析热度值
		hotValue := 0.0
		if hotValueStr != "" {
			if val, err := strconv.ParseFloat(strings.ReplaceAll(hotValueStr, "万", ""), 64); err == nil {
				hotValue = val
				if strings.Contains(hotValueStr, "万") {
					hotValue *= 10000
				}
			}
		}

		item := core.HotItem{
			Title:       title,
			Description: fmt.Sprintf("百度热搜: %s", title),
			URL:         link,
			HotValue:    hotValue,
			Platform:    "百度",
			Rank:        i + 1,
			CreatedAt:   time.Now(),
		}
		items = append(items, item)
	})

	if len(items) == 0 {
		return b.crawlBaiduFallback()
	}

	return items, nil
}

func (b *BaiduCrawler) crawlBaiduFallback() ([]core.HotItem, error) {
	// 百度备用数据
	mockData := []string{
		"最新科技动态", "社会热点新闻", "经济发展趋势",
		"文化教育资讯", "健康生活指南", "环保政策解读",
	}

	var items []core.HotItem
	for i, title := range mockData {
		item := core.HotItem{
			Title:       title,
			Description: fmt.Sprintf("百度热搜: %s", title),
			URL:         fmt.Sprintf("https://www.baidu.com/s?wd=%s", title),
			HotValue:    float64(60 - i*8),
			Platform:    "百度",
			Rank:        i + 1,
			CreatedAt:   time.Now(),
		}
		items = append(items, item)
	}

	return items, nil
}
