@echo off
chcp 65001 >nul
echo 🔨 构建新闻爬虫系统...

:: 创建bin目录
if not exist "bin" mkdir bin

:: 构建新闻爬取程序
echo 📰 构建新闻爬取程序 (news-crawler.exe)...
go build -o bin\news-crawler.exe cmd\main.go
if errorlevel 1 (
    echo ❌ 新闻爬取程序构建失败
    pause
    exit /b 1
)

:: 构建热榜内容生成程序
echo 🔥 构建热榜内容生成程序 (hotlist-generator.exe)...
go build -o bin\hotlist-generator.exe cmd\hotlist-generator.go
if errorlevel 1 (
    echo ❌ 热榜内容生成程序构建失败
    pause
    exit /b 1
)

:: 构建Web监控服务器
echo 🌐 构建Web监控服务器 (web-monitor.exe)...
go build -o bin\web-monitor.exe cmd\web-monitor.go
if errorlevel 1 (
    echo ❌ Web监控服务器构建失败
    pause
    exit /b 1
)



echo.
echo ✅ 构建完成！
echo.
echo 📋 可用程序（3个核心入口）：
echo   • bin\news-crawler.exe      - 新闻爬取程序（爬取+向量化）
echo   • bin\hotlist-generator.exe - 热榜内容生成程序（热榜+话题+文章+邮件）
echo   • bin\web-monitor.exe       - Web监控服务器（工作流监控界面）
echo.
echo 🚀 快速开始：
echo   新闻爬取: bin\news-crawler.exe -once
echo   热榜生成: bin\hotlist-generator.exe -once
echo   Web监控:  bin\web-monitor.exe
echo.
echo 📖 详细使用说明请查看 USAGE.md
pause
