# 编译输出
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 输出的覆盖率文件
*.out

# Go工作空间文件
go.work

# 依赖目录
vendor/

# 环境变量文件
.env
.env.local
.env.production

# 日志文件
logs/
*.log

# 数据文件
data/
*.csv

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/
*.tmp

# Docker相关
.dockerignore

# 备份文件
backups/
*.backup
*.bak

# 测试覆盖率
coverage.txt
coverage.html

# 性能分析文件
*.prof
*.pprof

# 构建缓存
.cache/
